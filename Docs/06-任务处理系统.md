# 任务处理系统设计

## 概述

任务处理系统支持同步和异步两种处理模式，使用FastAPI的BackgroundTasks和数据库任务表来管理任务，确保多进程部署时不会重复处理任务。

## 同步异步处理架构

### 处理模式选择
- **同步处理**：直接在请求线程中处理，适用于小文件和实时需求
- **异步处理**：使用FastAPI BackgroundTasks，适用于大文件和批量操作
- **混合模式**：根据文件大小和数量自动选择处理模式

### FastAPI BackgroundTasks优势
- **轻量级**：无需额外的消息队列组件
- **进程内**：与主应用共享内存和数据库连接
- **简单可靠**：自动处理异常和资源清理
- **扩展性**：支持多进程部署时的任务分发

## 多进程部署任务防重复机制

### 任务锁定策略
- **数据库锁**：使用数据库行级锁防止重复处理
- **超时机制**：任务锁定超时自动释放（30分钟）
- **状态原子更新**：使用数据库事务确保状态更新原子性

### 启动时任务恢复
- **扫描未完成任务**：启动时查询PENDING和LOCKED状态的任务
- **锁定超时检查**：检查锁定时间是否超时（默认30分钟）
- **任务重新分配**：将超时的任务重置为PENDING状态

## 任务处理流程

### 同步处理流程
- **接收请求**：验证文件和参数
- **直接处理**：在请求线程中提取特征
- **数据库更新**：保存特征向量和元数据
- **返回结果**：直接返回处理结果

### 异步处理流程
- **创建任务**：在tasks表中创建任务记录
- **后台处理**：使用BackgroundTasks启动处理
- **任务锁定**：原子性更新任务状态为RUNNING
- **特征提取**：处理文件并提取特征
- **结果保存**：更新数据库和任务状态
- **状态查询**：提供任务状态查询接口

### 任务恢复流程
- **启动扫描**：应用启动时扫描tasks表
- **超时检查**：检查LOCKED状态任务的锁定时间
- **任务重置**：将超时任务状态重置为PENDING
- **重新处理**：将PENDING任务加入处理队列

## 任务状态管理

### 任务状态枚举
- **PENDING**: 等待开始
- **RUNNING**: 正在执行
- **COMPLETED**: 执行完成
- **FAILED**: 执行失败
- **CANCELLED**: 已取消
- **LOCKED**: 已锁定（防止多进程重复处理）

### 任务类型枚举
- **SINGLE_FILE**: 单文件处理
- **BATCH_FILES**: 批量文件处理
- **DIRECTORY_SCAN**: 目录扫描

## API接口设计

### 单文件添加
```python
@router.post("/content/add-file", response_model=ApiResponse[AddFileResultData])
async def add_file(
    request: AddFileRequest,
    async_mode: bool = False
) -> ApiResponse[AddFileResultData]:
    pass
```
- 支持文件上传或文件路径，扩展属性参数（任意JSON结构）
- 查询参数：`async=true/false`（默认false同步处理）
- 返回：`ApiResponse[AddFileResultData]`格式

### 批量文件添加
```python
@router.post("/content/add-batch", response_model=ApiResponse[BatchTaskData])
async def add_batch_files(
    request: AddBatchRequest
) -> ApiResponse[BatchTaskData]:
    pass
```
- 支持多文件上传或路径列表，为每个文件指定不同的扩展属性
- 默认异步处理，返回：`ApiResponse[BatchTaskData]`格式

### 同步vs异步处理策略
- **同步处理**：适用于单文件、小文件、实时性要求高的场景
- **异步处理**：适用于批量文件、大文件、可容忍延迟的场景
- **自动降级**：同步处理超时自动转为异步处理

### 任务状态管理
```python
@router.get("/tasks/{task_id}", response_model=ApiResponse[TaskDetailData])
async def get_task_status(task_id: str) -> ApiResponse[TaskDetailData]:
    pass

@router.get("/tasks", response_model=ApiResponse[TaskListData])
async def get_task_list(
    page: int = 1, 
    size: int = 20
) -> ApiResponse[TaskListData]:
    pass

@router.post("/tasks/{task_id}/cancel", response_model=ApiResponse[None])
async def cancel_task(task_id: str) -> ApiResponse[None]:
    pass
```
- 支持任务查询、取消、重试、删除等操作
- 所有接口都使用统一的`ApiResponse[T]`格式

## 设计模式实现

### 观察者模式 - 任务状态通知

#### 任务状态观察者接口
```python
# app/core/tasks/observer.py
from abc import ABC, abstractmethod
from typing import Dict, Any

class TaskObserver(ABC):
    """任务观察者抽象基类"""

    @abstractmethod
    def on_task_created(self, task_id: str, task_data: Dict[str, Any]) -> None:
        """任务创建时回调"""
        pass

    @abstractmethod
    def on_task_started(self, task_id: str, task_data: Dict[str, Any]) -> None:
        """任务开始时回调"""
        pass

    @abstractmethod
    def on_task_progress(self, task_id: str, progress: float, message: str = "") -> None:
        """任务进度更新回调"""
        pass

    @abstractmethod
    def on_task_completed(self, task_id: str, result: Dict[str, Any]) -> None:
        """任务完成时回调"""
        pass

    @abstractmethod
    def on_task_failed(self, task_id: str, error: str) -> None:
        """任务失败时回调"""
        pass

class LoggingObserver(TaskObserver):
    """日志记录观察者"""

    def __init__(self):
        from app.utils.logger import get_logger
        self.logger = get_logger(__name__)

    def on_task_created(self, task_id: str, task_data: Dict[str, Any]) -> None:
        self.logger.info(f"任务创建: {task_id}", extra={"task_data": task_data})

    def on_task_started(self, task_id: str, task_data: Dict[str, Any]) -> None:
        self.logger.info(f"任务开始: {task_id}")

    def on_task_progress(self, task_id: str, progress: float, message: str = "") -> None:
        self.logger.info(f"任务进度: {task_id} - {progress:.1%} {message}")

    def on_task_completed(self, task_id: str, result: Dict[str, Any]) -> None:
        self.logger.info(f"任务完成: {task_id}", extra={"result": result})

    def on_task_failed(self, task_id: str, error: str) -> None:
        self.logger.error(f"任务失败: {task_id} - {error}")

class DatabaseObserver(TaskObserver):
    """数据库状态更新观察者"""

    def __init__(self):
        from app.database import get_db_session
        self.get_session = get_db_session

    async def on_task_created(self, task_id: str, task_data: Dict[str, Any]) -> None:
        # 更新数据库任务状态
        pass

    async def on_task_started(self, task_id: str, task_data: Dict[str, Any]) -> None:
        # 更新任务开始时间
        pass

    async def on_task_progress(self, task_id: str, progress: float, message: str = "") -> None:
        # 更新任务进度
        pass

    async def on_task_completed(self, task_id: str, result: Dict[str, Any]) -> None:
        # 更新任务完成状态
        pass

    async def on_task_failed(self, task_id: str, error: str) -> None:
        # 更新任务失败状态
        pass
```

#### 任务主题（被观察者）
```python
# app/core/tasks/subject.py
from typing import List
from .observer import TaskObserver

class TaskSubject:
    """任务主题 - 被观察者"""

    def __init__(self):
        self._observers: List[TaskObserver] = []

    def attach(self, observer: TaskObserver) -> None:
        """添加观察者"""
        if observer not in self._observers:
            self._observers.append(observer)

    def detach(self, observer: TaskObserver) -> None:
        """移除观察者"""
        if observer in self._observers:
            self._observers.remove(observer)

    def notify_task_created(self, task_id: str, task_data: dict) -> None:
        """通知任务创建"""
        for observer in self._observers:
            observer.on_task_created(task_id, task_data)

    def notify_task_started(self, task_id: str, task_data: dict) -> None:
        """通知任务开始"""
        for observer in self._observers:
            observer.on_task_started(task_id, task_data)

    def notify_task_progress(self, task_id: str, progress: float, message: str = "") -> None:
        """通知任务进度"""
        for observer in self._observers:
            observer.on_task_progress(task_id, progress, message)

    def notify_task_completed(self, task_id: str, result: dict) -> None:
        """通知任务完成"""
        for observer in self._observers:
            observer.on_task_completed(task_id, result)

    def notify_task_failed(self, task_id: str, error: str) -> None:
        """通知任务失败"""
        for observer in self._observers:
            observer.on_task_failed(task_id, error)
```

### 建造者模式 - 任务构建器

```python
# app/core/tasks/builder.py
from typing import Dict, Any, Optional
from enum import Enum

class TaskType(Enum):
    SINGLE_FILE = "single_file"
    BATCH_FILES = "batch_files"
    DIRECTORY_SCAN = "directory_scan"

class TaskBuilder:
    """任务构建器"""

    def __init__(self):
        self.reset()

    def reset(self) -> 'TaskBuilder':
        """重置构建器"""
        self._task = {
            "id": None,
            "type": None,
            "priority": 0,
            "params": {},
            "metadata": {},
            "timeout": 3600,  # 默认1小时超时
            "retry_count": 3,
            "created_by": None
        }
        return self

    def set_id(self, task_id: str) -> 'TaskBuilder':
        """设置任务ID"""
        self._task["id"] = task_id
        return self

    def set_type(self, task_type: TaskType) -> 'TaskBuilder':
        """设置任务类型"""
        self._task["type"] = task_type.value
        return self

    def set_priority(self, priority: int) -> 'TaskBuilder':
        """设置任务优先级"""
        self._task["priority"] = priority
        return self

    def add_file(self, file_path: str, metadata: Optional[Dict] = None) -> 'TaskBuilder':
        """添加文件"""
        if "files" not in self._task["params"]:
            self._task["params"]["files"] = []

        file_info = {"path": file_path}
        if metadata:
            file_info["metadata"] = metadata

        self._task["params"]["files"].append(file_info)
        return self

    def set_batch_size(self, batch_size: int) -> 'TaskBuilder':
        """设置批处理大小"""
        self._task["params"]["batch_size"] = batch_size
        return self

    def set_timeout(self, timeout: int) -> 'TaskBuilder':
        """设置超时时间"""
        self._task["timeout"] = timeout
        return self

    def add_metadata(self, key: str, value: Any) -> 'TaskBuilder':
        """添加元数据"""
        self._task["metadata"][key] = value
        return self

    def build(self) -> Dict[str, Any]:
        """构建任务"""
        if not self._task["id"]:
            import uuid
            self._task["id"] = str(uuid.uuid4())

        if not self._task["type"]:
            raise ValueError("任务类型不能为空")

        return self._task.copy()

# 使用示例
def create_single_file_task(file_path: str, metadata: Dict = None) -> Dict[str, Any]:
    """创建单文件处理任务"""
    builder = TaskBuilder()
    task = (builder
            .set_type(TaskType.SINGLE_FILE)
            .add_file(file_path, metadata)
            .set_priority(1)
            .set_timeout(1800)  # 30分钟
            .build())
    return task

def create_batch_task(file_paths: List[str], batch_size: int = 4) -> Dict[str, Any]:
    """创建批量处理任务"""
    builder = TaskBuilder()
    builder.set_type(TaskType.BATCH_FILES).set_batch_size(batch_size)

    for file_path in file_paths:
        builder.add_file(file_path)

    return builder.build()
```

## 技术实现细节

### 任务锁定机制
使用数据库UPDATE...WHERE...RETURNING语句实现原子性任务锁定，防止多进程重复处理。

### 启动时任务恢复
查询PENDING和LOCKED状态的任务，检查锁定超时状态，重新分配需要恢复的任务。

## 性能优化

### 任务处理优化
- **批量处理**：合并小任务减少数据库操作，参考Demo的批量机制
- **连接池**：复用数据库连接减少开销
- **内存管理**：及时释放处理完成的文件内存，避免内存泄漏
- **错误隔离**：单个文件失败不影响批量任务
- **智能调度**：根据系统负载动态调整任务并发数
- **优先级队列**：实时添加任务优先于批量扫描任务

## 配置参数

### 任务处理配置
- **SYNC_PROCESS_TIMEOUT**: 同步处理超时时间（秒）
- **TASK_LOCK_TIMEOUT**: 任务锁定超时时间（分钟）
- **MAX_RETRY_ATTEMPTS**: 最大重试次数
- **TASK_CLEANUP_INTERVAL**: 任务清理间隔（小时）
- **BACKGROUND_TASK_WORKERS**: 后台任务工作线程数

### 实时添加配置
- **REALTIME_PROCESS_ENABLED**: 是否启用实时处理
- **MAX_CONCURRENT_UPLOADS**: 最大并发上传数
- **UPLOAD_TIMEOUT**: 上传超时时间
- **AUTO_ASYNC_THRESHOLD**: 自动转异步的文件大小阈值

## 监控和维护

### 任务监控
- **任务状态统计**：各状态任务数量统计
- **处理性能监控**：任务处理时间和成功率
- **资源使用监控**：内存和CPU使用情况
- **错误率监控**：失败任务比例和错误类型

### 维护操作
- **任务清理**：定期清理完成的任务记录
- **失败重试**：自动或手动重试失败的任务
- **状态修复**：修复异常状态的任务
- **性能调优**：根据监控数据调整配置参数
