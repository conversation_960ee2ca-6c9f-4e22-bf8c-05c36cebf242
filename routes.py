# === Notice / 注意 ===
# The following code has been intentionally obfuscated to prevent the removal or tampering of copyright and attribution information.
# 下列代码已故意混淆，以防止版权和署名信息被删除或篡改。
#
# This is NOT intended to limit legitimate use of this open-source project under its license.
# 此举并非为了限制用户在遵循开源许可证前提下的合法使用。
#
# Please do not attempt to bypass or modify this section to remove copyright or attribution.
# 请勿尝试绕过或修改此部分代码以移除版权或署名信息。
#
# To ensure compliance with the license, please retain all copyright notices.
# 为遵守许可证条款，请保留所有版权声明。
#
# We appreciate your respect for the original authorship.
# 感谢您对原创作者的尊重。
import logging
from functools import wraps
from io import BytesIO

from flask import Flask, abort, redirect, request, send_file, session, url_for, jsonify

from config import *
from database import get_image_path_by_id, is_video_exist
from models import DatabaseSession
from scan import scanner  # noqa
from utils import crop_video, get_hash, resize_image_with_aspect_ratio

logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = "https://github.com/chn-lee-yumi/MaterialSearch"


def login_required(view_func):
    """
    装饰器函数，用于控制需要登录认证的视图
    """

    @wraps(view_func)
    def wrapper(*args, **kwargs):
        if ENABLE_LOGIN:
            if "username" not in session:
                return redirect(url_for("login"))
        return view_func(*args, **kwargs)

    return wrapper


@app.route("/", methods=["GET"])
@login_required
def index_page():
    return app.send_static_file("index.html")


@app.route("/login", methods=["GET", "POST"])
def login():
    if request.method == "POST":
        ip_addr = request.environ.get("HTTP_X_FORWARDED_FOR", request.remote_addr)
        username = request.form["username"]
        password = request.form["password"]
        if username == USERNAME and password == PASSWORD:
            logger.info(f"用户登录成功 {ip_addr}")
            session["username"] = username
            return redirect(url_for("index_page"))
        logger.info(f"用户登录失败 {ip_addr}")
        return redirect(url_for("login"))
    return app.send_static_file("login.html")


@app.route("/api/scan", methods=["GET"])
@login_required
def api_scan():
    global scanner
    if not scanner.is_scanning:
        import threading
        scan_thread = threading.Thread(target=scanner.scan, args=(False,))
        scan_thread.start()
        return jsonify({"status": "start scanning"})
    return jsonify({"status": "already scanning"})


@app.route("/logout", methods=["GET", "POST"])
def logout():
    session.clear()
    return redirect(url_for("login"))


@app.route("/api/get_image/<int:image_id>", methods=["GET"])
@login_required
def api_get_image(image_id):
    with DatabaseSession() as session_db:
        path = get_image_path_by_id(session_db, image_id)
        logger.debug(path)
    if request.args.get("thumbnail") == "1" and os.path.splitext(path)[-1] != "gif":
        image = resize_image_with_aspect_ratio(path, (640, 480), convert_rgb=True)
        image_io = BytesIO()
        image.save(image_io, 'JPEG', quality=60)
        image_io.seek(0)
        return send_file(image_io, mimetype='image/jpeg', download_name="thumbnail_" + os.path.basename(path))
    return send_file(path)


@app.route("/api/get_video/<video_path>", methods=["GET"])
@login_required
def api_get_video(video_path):
    path = base64.urlsafe_b64decode(video_path).decode()
    logger.debug(path)
    with DatabaseSession() as session_db:
        if not is_video_exist(session_db, path):
            abort(404)
    return send_file(path)


@app.route(
    "/api/download_video_clip/<video_path>/<int:start_time>/<int:end_time>",
    methods=["GET"],
)
@login_required
def api_download_video_clip(video_path, start_time, end_time):
    path = base64.urlsafe_b64decode(video_path).decode()
    logger.debug(path)
    with DatabaseSession() as session_db:
        if not is_video_exist(session_db, path):
            abort(404)
    start_time -= VIDEO_EXTENSION_LENGTH
    end_time += VIDEO_EXTENSION_LENGTH
    if start_time < 0:
        start_time = 0
    output_path = f"{TEMP_PATH}/video_clips/{start_time}_{end_time}_" + os.path.basename(path)
    if not os.path.exists(output_path):
        crop_video(path, output_path, start_time, end_time)
    return send_file(output_path)


@app.route("/api/upload", methods=["POST"])
@login_required
def api_upload():
    logger.debug(request.files)
    upload_file_path = session.get('upload_file_path', '')
    if upload_file_path and os.path.exists(upload_file_path):
        os.remove(upload_file_path)
    f = request.files["file"]
    filehash = get_hash(f.stream)
    upload_file_path = f"{TEMP_PATH}/upload/{filehash}"
    f.save(upload_file_path)
    session['upload_file_path'] = upload_file_path
    return "file uploaded successfully"


import lzma, base64

exec(lzma.decompress(base64.b64decode(
    '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')))
