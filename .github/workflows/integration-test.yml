name: Integration Test

on:
  pull_request:
    branches: [ "main", "dev" ]
  push:
    branches: [ "testing" ]
  workflow_call:

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, ubuntu-24.04-arm]  # 支持 Linux 和 Windows
        python-version: ["3.12"]  # 测试 Python 3.12
        
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install winget (only on Windows)
        if: runner.os == 'Windows'
        uses: Cyberboss/install-winget@v1

      - name: Install requirements
        run: |
          python -m pip install --upgrade pip
          pip install pytest
          if [ "${{ matrix.os }}" == "windows-latest" ]; then
            powershell -ExecutionPolicy Bypass ./install_ffmpeg.ps1
            pip install -r requirements_windows.txt
          else
            sudo apt-get update && sudo apt-get install -y ffmpeg
            pip install -r requirements.txt
          fi
        shell: bash

      - name: Create .env file
        run: echo "ASSETS_PATH=./" > .env

      - name: Run main.py
        run: |
          if [ "${{ matrix.os }}" == "windows-latest" ]; then
            set PYTHONUNBUFFERED=1
            start /min python main.py > run_log.txt 2>&1
          else
            nohup python main.py > run_log.txt 2>&1 &
          fi
        shell: bash

      - name: Read run log
        run: |
          sleep 10
          cat run_log.txt
        shell: bash

#      - name: Check run_log.txt for success message
#        run: |
#          echo "========== Run log: =========="
#          cat run_log.txt
#          if grep -q "Running on http://127.0.0.1:8085" run_log.txt; then
#            echo "✅ Found expected message. Test passed."
#          else
#            echo "❌ Expected message not found."
#            exit 1
#          fi
#        shell: bash

#      - name: Test API
#        run: |
#          pytest
#          echo "========== Run log: =========="
#          cat run_log.txt
#        shell: bash
