name: Docker Image Release CI

on:
  release:
    types: [created]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:

    - name: Checkout
      uses: actions/checkout@v4
    
    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3
      
    # https://dev.to/cloudx/multi-arch-docker-images-the-easy-way-with-github-actions-4k54
    - name: Set up Docker Buildx
      id: buildx
      uses: docker/setup-buildx-action@v3
          
    - name: Login to <PERSON><PERSON>
      uses: docker/login-action@v3
      with:
        registry: registry.cn-hongkong.aliyuncs.com
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_HUB_USERNAME }}
        password: ${{ secrets.DOCKER_HUB_PASSWORD }}
      
    - name: Build and push
      uses: docker/build-push-action@v6
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: |
          yumilee/materialsearch:${{ github.event.release.tag_name }}
          registry.cn-hongkong.aliyuncs.com/chn-lee-yumi/materialsearch:${{ github.event.release.tag_name }}

    #- name: enable debug interface
    #  uses: chn-lee-yumi/debugger-action@master
