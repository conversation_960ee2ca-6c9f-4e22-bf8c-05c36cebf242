# MaterialSearch 搜索性能优化

## 概述

搜索性能优化是MaterialSearch系统的核心，通过多层次的优化策略，确保在大规模数据集下仍能提供毫秒级的搜索响应。本文档详细介绍了系统采用的各种性能优化技术。

## 1. 快速匹配机制

### 以图搜图优化流程

基于Demo代码分析，系统采用了高效的搜索策略：

#### 单次特征提取
- 搜索图片只需进行一次特征提取（search.py:117-119）
- 避免重复计算，显著提升响应速度
- 特征向量缓存机制，避免重复推理

#### 批量相似度计算
- 直接从数据库读取所有预处理的特征向量（search.py:53-58）
- 使用numpy进行矩阵运算，通过match_batch函数实现高效批量匹配（process_assets.py:220-246）
- 一次性计算所有候选项的相似度，避免逐个比较

#### 向量运算优化
```python
# 核心相似度计算（process_assets.py:239）
positive_scores = image_features @ positive_feature.T

# 批量阈值过滤（process_assets.py:243-245）
scores = np.where(positive_scores < positive_threshold / 100, 0, positive_scores)
```

### 向量索引加速

#### pgvector索引类型

**IVFFlat索引**
- **适用场景**：中等规模数据集（1万-100万向量）
- **查询性能**：较快的近似查询
- **内存占用**：相对较低
- **配置示例**：
```sql
CREATE INDEX idx_images_features_ivfflat 
ON images USING ivfflat(features vector_cosine_ops) 
WITH (lists = 100);
```

**HNSW索引**
- **适用场景**：大规模数据集（100万+向量）
- **查询性能**：更快的近似查询
- **内存占用**：相对较高
- **配置示例**：
```sql
CREATE INDEX idx_images_features_hnsw 
ON images USING hnsw(features vector_cosine_ops) 
WITH (m = 16, ef_construction = 64);
```

#### 索引参数调优

**IVFFlat参数**
- **lists**：聚类中心数量，推荐为 rows/1000
- **probe**：查询时检查的聚类数，影响召回率和速度

**HNSW参数**
- **m**：每个节点的连接数，影响查询精度和构建时间
- **ef_construction**：构建时的候选数，影响索引质量

## 2. 缓存策略优化

### LRU缓存机制

#### 搜索结果缓存
```python
from functools import lru_cache

@lru_cache(maxsize=64)  # 通过CACHE_SIZE参数控制
def cached_search(query_hash: str, filters_hash: str):
    """
    缓存搜索结果，避免重复计算
    """
    # 实际搜索逻辑
    pass
```

#### 缓存策略
- **搜索结果缓存**：使用@lru_cache装饰器缓存搜索结果（search.py:99-100）
- **缓存大小**：通过CACHE_SIZE参数控制缓存条目数量（默认64）
- **缓存清理**：扫描完成后自动清空缓存，确保数据一致性
- **分类缓存**：图片搜索和视频搜索分开缓存

### 特征向量预处理

#### 归一化处理
- **特征向量归一化**：特征向量在提取时进行归一化（process_assets.py:33-34）
- **便于计算**：归一化后的向量直接用于余弦相似度计算
- **内存优化**：特征向量以二进制格式存储，减少内存占用

#### 预计算优化
```python
# 特征向量归一化和存储
def normalize_and_store_features(features: np.ndarray) -> bytes:
    """
    归一化特征向量并转换为存储格式
    """
    # L2归一化
    normalized = features / np.linalg.norm(features, axis=1, keepdims=True)
    
    # 转换为bytes格式存储
    return normalized.astype(np.float32).tobytes()
```

### 多级缓存架构

#### 内存缓存
- **模型缓存**：CLIP模型实例缓存
- **特征缓存**：常用特征向量缓存
- **查询缓存**：搜索结果缓存

#### 数据库缓存
- **连接池**：复用数据库连接
- **查询计划缓存**：PostgreSQL查询计划缓存
- **页面缓存**：数据库页面缓存

## 3. 批量处理优化

### 扫描时批量处理

#### 批量大小配置
```python
# 通过SCAN_PROCESS_BATCH_SIZE控制批处理大小
BATCH_SIZE = int(os.getenv("SCAN_PROCESS_BATCH_SIZE", "4"))

def process_images_batch(image_paths: List[str]):
    """
    批量处理图片，提高GPU利用率
    """
    for i in range(0, len(image_paths), BATCH_SIZE):
        batch = image_paths[i:i + BATCH_SIZE]
        # 批量推理
        features = model.encode_images(batch)
        # 批量存储
        store_features_batch(batch, features)
```

#### GPU利用率优化
- **批量推理**：批量处理提高GPU利用率，减少推理次数
- **内存管理**：达到批量大小后统一处理，避免内存溢出
- **流水线处理**：数据预处理与模型推理并行

### 视频帧处理优化

#### 帧间隔控制
- **智能帧选择**：通过FRAME_INTERVAL参数控制帧提取间隔
- **关键帧检测**：优先提取场景变化较大的帧
- **批量推理**：视频帧也采用批量处理机制

#### 生成器模式
```python
def process_video_frames(video_path: str, frame_interval: int = 2):
    """
    使用生成器模式处理大视频文件，避免内存溢出
    """
    cap = cv2.VideoCapture(video_path)
    frame_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
            
        if frame_count % (frame_interval * fps) == 0:
            yield frame, frame_count / fps
            
        frame_count += 1
    
    cap.release()
```

## 4. 数据库查询优化

### 高效数据读取

#### 批量读取优化
```python
# 批量读取特征向量（search.py:54）
async def get_image_features_batch(session, filters):
    query = """
    SELECT id, path, features 
    FROM images 
    WHERE process_status = 'COMPLETED'
    """
    
    if filters:
        query += " AND metadata @> $1"
        result = await session.fetch(query, json.dumps(filters))
    else:
        result = await session.fetch(query)
    
    # 高效的向量重构
    ids = [row['id'] for row in result]
    paths = [row['path'] for row in result]
    features = np.frombuffer(
        b"".join([row['features'] for row in result]), 
        dtype=np.float32
    ).reshape(len(result), -1)
    
    return ids, paths, features
```

#### 查询性能监控
```python
import time

async def monitored_search(query, filters):
    start_time = time.time()
    
    # 执行搜索
    results = await perform_search(query, filters)
    
    query_time = time.time() - start_time
    
    # 记录性能日志
    logger.info(f"Search completed in {query_time:.3f}s, results: {len(results)}")
    
    # 记录慢查询
    if query_time > 1.0:
        logger.warning(f"Slow search detected: {query_time:.3f}s")
    
    return results, query_time
```

### 索引策略优化

#### 复合索引
```sql
-- 为常用查询组合创建复合索引
CREATE INDEX idx_images_status_type 
ON images (process_status, source_type);

-- 为元数据查询创建表达式索引
CREATE INDEX idx_images_category 
ON images ((metadata->>'category'));
```

#### 部分索引
```sql
-- 只为已处理的记录创建向量索引
CREATE INDEX idx_images_features_processed 
ON images USING ivfflat(features vector_cosine_ops) 
WHERE process_status = 'COMPLETED';
```

## 5. CPU环境优化

### CPU性能优化策略

#### CPU环境检测和优化
```python
def optimize_cpu_environment():
    """
    CPU环境优化配置
    针对CPU环境进行专门优化
    """
    import os
    import multiprocessing

    cpu_count = multiprocessing.cpu_count()

    # 根据CPU核心数设置线程
    if cpu_count >= 8:
        torch_threads = 4
        onnx_threads = 4
        batch_size = 4
    elif cpu_count >= 4:
        torch_threads = 2
        onnx_threads = 2
        batch_size = 2
    else:
        torch_threads = 1
        onnx_threads = 1
        batch_size = 1

    # 设置CPU优化环境变量
    os.environ['OMP_NUM_THREADS'] = str(torch_threads)
    os.environ['MKL_NUM_THREADS'] = str(torch_threads)
    os.environ['NUMEXPR_NUM_THREADS'] = str(torch_threads)

    return {
        "device": "cpu",
        "torch_threads": torch_threads,
        "onnx_threads": onnx_threads,
        "recommended_batch_size": batch_size
    }
```

#### CPU内存配置建议

基于CPU环境的配置经验：

**8GB内存配置**
```python
MODEL_CONFIG = {
    "model_name": "OFA-Sys/chinese-clip-vit-base-patch16",
    "batch_size": 1,
    "precision": "fp32",  # CPU环境使用fp32
    "device": "cpu",
    "cpu_threads": 2
}
```

**16GB内存配置**
```python
MODEL_CONFIG = {
    "model_name": "OFA-Sys/chinese-clip-vit-base-patch16",
    "batch_size": 2,
    "precision": "fp32",
    "device": "cpu",
    "cpu_threads": 4
}
```

**16GB+显存配置**
```python
MODEL_CONFIG = {
    "model_name": "OFA-Sys/chinese-clip-vit-large-patch14-336px",
    "batch_size": 16,
    "precision": "fp16",
    "max_memory": "15GB"
}
```

### 模型选择策略

#### 中文模型选择
```python
# 根据硬件能力选择合适的模型
MODEL_SELECTION = {
    "small": "OFA-Sys/chinese-clip-vit-base-patch16",      # 约400MB
    "medium": "OFA-Sys/chinese-clip-vit-large-patch14",   # 约800MB
    "large": "OFA-Sys/chinese-clip-vit-large-patch14-336px", # 约800MB
    "xlarge": "OFA-Sys/chinese-clip-vit-huge-patch14"     # 约2GB
}

def select_model_by_memory(available_memory_gb: float):
    if available_memory_gb < 2:
        return MODEL_SELECTION["small"]
    elif available_memory_gb < 4:
        return MODEL_SELECTION["medium"]
    elif available_memory_gb < 8:
        return MODEL_SELECTION["large"]
    else:
        return MODEL_SELECTION["xlarge"]
```

## 6. 搜索阈值优化

### 阈值参数配置

#### 动态阈值调整
```python
class SearchConfig:
    def __init__(self):
        self.positive_threshold = float(os.getenv("POSITIVE_THRESHOLD", "36"))
        self.negative_threshold = float(os.getenv("NEGATIVE_THRESHOLD", "36"))
        self.image_threshold = float(os.getenv("IMAGE_THRESHOLD", "85"))
    
    def adjust_threshold_by_data_size(self, data_size: int):
        """
        根据数据规模动态调整阈值
        """
        if data_size > 1000000:  # 100万+
            self.positive_threshold *= 1.1  # 提高阈值，减少召回
        elif data_size < 10000:  # 1万以下
            self.positive_threshold *= 0.9  # 降低阈值，增加召回
```

#### 自适应阈值
```python
def adaptive_threshold(query_results: List, target_count: int = 20):
    """
    自适应调整阈值，确保返回合适数量的结果
    """
    if len(query_results) == 0:
        return 0.0  # 降低阈值
    
    scores = [result['similarity_score'] for result in query_results]
    scores.sort(reverse=True)
    
    if len(scores) >= target_count:
        return scores[target_count - 1]  # 取第N个结果的分数作为阈值
    else:
        return min(scores) * 0.8  # 适当降低阈值
```

## 7. 内存和存储优化

### 特征向量存储

#### 二进制格式优化
```python
def optimize_vector_storage(features: np.ndarray) -> bytes:
    """
    优化特征向量存储格式
    """
    # 使用float32而非float64，减少一半存储空间
    features_f32 = features.astype(np.float32)
    
    # L2归一化
    normalized = features_f32 / np.linalg.norm(features_f32, axis=1, keepdims=True)
    
    # 转换为bytes格式
    return normalized.tobytes()

def load_optimized_vectors(vector_bytes: bytes, count: int, dim: int) -> np.ndarray:
    """
    快速加载优化的向量格式
    """
    return np.frombuffer(vector_bytes, dtype=np.float32).reshape(count, dim)
```

#### 压缩存储
```python
import zlib

def compress_features(features: np.ndarray) -> bytes:
    """
    压缩特征向量，进一步减少存储空间
    """
    features_bytes = features.astype(np.float32).tobytes()
    return zlib.compress(features_bytes, level=6)

def decompress_features(compressed_bytes: bytes, shape: tuple) -> np.ndarray:
    """
    解压缩特征向量
    """
    decompressed = zlib.decompress(compressed_bytes)
    return np.frombuffer(decompressed, dtype=np.float32).reshape(shape)
```

### 内存管理优化

#### 内存池管理
```python
class MemoryPool:
    def __init__(self, max_size_mb: int = 1024):
        self.max_size = max_size_mb * 1024 * 1024
        self.current_size = 0
        self.cache = {}
    
    def get_features(self, key: str) -> Optional[np.ndarray]:
        if key in self.cache:
            return self.cache[key]
        return None
    
    def store_features(self, key: str, features: np.ndarray):
        feature_size = features.nbytes
        
        # 检查内存限制
        while self.current_size + feature_size > self.max_size and self.cache:
            # LRU淘汰
            oldest_key = next(iter(self.cache))
            old_features = self.cache.pop(oldest_key)
            self.current_size -= old_features.nbytes
        
        self.cache[key] = features
        self.current_size += feature_size
```

#### 垃圾回收优化
```python
import gc

def optimize_memory_usage():
    """
    优化内存使用，定期清理
    """
    # 强制垃圾回收
    gc.collect()
    
    # 清理PyTorch缓存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 清理numpy临时数组
    # 在处理大批量数据后调用
```

## 8. 性能监控和调优

### 性能指标监控

#### 关键指标
```python
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'search_count': 0,
            'total_search_time': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'model_inference_time': 0,
            'db_query_time': 0
        }
    
    def record_search(self, search_time: float, cache_hit: bool):
        self.metrics['search_count'] += 1
        self.metrics['total_search_time'] += search_time
        
        if cache_hit:
            self.metrics['cache_hits'] += 1
        else:
            self.metrics['cache_misses'] += 1
    
    def get_stats(self):
        total_searches = self.metrics['search_count']
        if total_searches == 0:
            return {}
        
        return {
            'avg_search_time': self.metrics['total_search_time'] / total_searches,
            'cache_hit_rate': self.metrics['cache_hits'] / total_searches,
            'searches_per_second': total_searches / self.metrics['total_search_time']
        }
```

### 性能基准测试

#### 基准测试工具
```python
def benchmark_search_performance():
    """
    性能基准测试
    """
    test_queries = [
        "红色汽车",
        "蓝色天空", 
        "绿色植物",
        "黄色花朵"
    ]
    
    results = {}
    
    for query in test_queries:
        start_time = time.time()
        
        # 执行搜索
        search_results = search_images_by_text(query, limit=20)
        
        end_time = time.time()
        search_time = end_time - start_time
        
        results[query] = {
            'search_time': search_time,
            'result_count': len(search_results),
            'avg_similarity': np.mean([r['similarity_score'] for r in search_results])
        }
    
    return results
```

## 9. 实际性能数据

### 搜索性能表现

基于Demo实际测试数据：

#### 响应时间分布
- **特征提取时间**：单张图片约50-200ms（取决于硬件）
- **相似度计算**：10万张图片批量计算约100-500ms
- **数据库查询**：特征向量读取约10-50ms
- **总搜索时间**：通常在1秒以内完成

#### 吞吐量指标
- **图片处理速度**：批量处理可达100-500张/分钟
- **视频处理速度**：取决于帧间隔和视频长度
- **并发搜索**：支持10-50个并发搜索请求

### 资源占用情况

#### 内存使用
- **512维特征向量**：约2KB/张图片
- **模型内存占用**：400MB-2GB（取决于模型大小）
- **缓存内存**：可配置，建议1-4GB

#### 存储空间
- **特征向量存储**：约为原图片大小的0.1-1%
- **数据库空间**：包含索引约为向量数据的2-3倍
- **日志文件**：可配置轮转和压缩

## 10. 调优建议

### 系统调优清单

#### 基础优化
1. **确保使用GPU加速**：CUDA > MPS > CPU
2. **合理设置批量大小**：根据显存调整BATCH_SIZE
3. **启用向量索引**：IVFFlat或HNSW
4. **配置合适的缓存大小**：CACHE_SIZE = 64-512

#### 高级优化
1. **数据库连接池**：设置合适的连接数
2. **查询并行化**：使用异步查询
3. **结果分页**：避免返回过多结果
4. **定期维护**：VACUUM和ANALYZE数据库

#### 监控指标
1. **搜索响应时间**：< 1秒
2. **缓存命中率**：> 70%
3. **GPU利用率**：> 80%
4. **内存使用率**：< 90%

### 故障排除

#### 常见性能问题
1. **搜索速度慢**：检查索引、缓存、硬件配置
2. **内存不足**：降低批量大小、清理缓存
3. **GPU OOM**：减少BATCH_SIZE、使用FP16
4. **数据库慢查询**：优化索引、检查查询计划

通过以上多层次的性能优化策略，MaterialSearch能够在大规模数据集下提供高效、稳定的搜索服务。 