<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Material Search Engine</title>
    <link rel="stylesheet" href="static/assets/index.css"/>
    <script src="static/assets/vue.global.prod.js"></script>
    <script src="static/assets/vue-i18n.global.prod.js"></script>
    <script src="static/assets/index.full.min.js"></script>
    <script src="static/assets/index.iife.min.js"></script>
    <script src="static/assets/clipboard.min.js"></script>
    <script src="static/assets/axios.min.js"></script>
    <script src="static/assets/index.js" defer></script>
</head>
<body>
<el-main id="app">
    <el-row justify="center">
        <h1 style="margin:0;">{{ $t("title") }}</h1>
        <!-- TODO: 可以手动切换语言
        <el-select v-model="locale" @change="changeLocale">
            <el-option label="English" value="en"></el-option>
            <el-option label="中文" value="zh"></el-option>
        </el-select>
        -->
    </el-row>

    <el-row justify="center">
        <el-tag size="large" effect="dark">{{ $t("scanStatus." + (isScanning ? "scanning" : "scanComplete")) }}</el-tag>
        <el-tag size="large" type="success" effect="dark">{{ $t("statusLabels.totalImages") }}: {{ status.total_images }}</el-tag>
        <el-tag size="large" type="success" effect="dark">{{ $t("statusLabels.totalVideos") }}: {{ status.total_videos }}</el-tag>
        <el-tag size="large" type="success" effect="dark">{{ $t("statusLabels.totalVideoFrames") }}: {{ status.total_video_frames }}</el-tag>
        <el-tag size="large" v-if="status.total_pexels_videos != 0" type="success" effect="dark">
            {{ $t("statusLabels.totalPexelsVideos") }}: {{status.total_pexels_videos }}
        </el-tag>
        <el-tag size="large" v-if="isScanning" type="info" effect="dark">
            {{ $t("statusLabels.scanningFiles") }}: {{ status.scanning_files === 0 && status.remain_files === 0 ? $t('statusLabels.calculating') : status.scanning_files }}
        </el-tag>
        <el-tag size="large" v-if="isScanning" type="info" effect="dark">
            {{ $t("statusLabels.remainFiles") }}: {{ status.scanning_files === 0 && status.remain_files === 0 ? $t('statusLabels.calculating') : status.remain_files }}
        </el-tag>
        <el-tag size="large" v-if="isScanning" type="info" effect="dark">{{ $t("statusLabels.remainTime") }}: {{ status.remain_time }}</el-tag>
        <el-tag size="large" v-if="isScanning" type="danger" effect="dark">
            {{ $t("statusLabels.scanProgress") }}: {{ Math.trunc(status.progress * 100) }}%
        </el-tag>
        <el-button-group v-if="!isScanning">
            <el-button round type="primary" size="default" @click="scan">{{ $t("buttons.scan") }}</el-button>
        </el-button-group>
        <!--<el-button-group>-->
        <!--    <el-button round type="warning" size="small" @click="cleanCache">{{ $t("buttons.cleanCache") }}</el-button>-->
        <!--</el-button-group>-->
        <el-button-group v-if="enableLogin">
            <a href="/logout">
                <el-button round type="danger" size="default">{{ $t("buttons.logout") }}</el-button>
            </a>
        </el-button-group>
        <el-progress style="width:100%" v-if="isScanning" :percentage="Math.trunc(status.progress*100)" :stroke-width="10"></el-progress>
    </el-row>

    <el-row>
        <el-tabs type="border-card" v-model="currentTab">

            <el-tab-pane :label="$t('searchTabs.textSearch')" name="0">
                <el-form ref="form" :model="form" :inline="true">
                    <el-row>
                        <el-col :span="18">
                            <el-input :placeholder="$t('formPlaceholders.positiveSearch')" v-model="form.positive" class="input-with-select"
                                  @keyup.enter.native="search(0)"></el-input>
                        </el-col>
                        <el-col :span="6">
                            <el-select v-model="form.top_n" :placeholder="$t('formPlaceholders.topNResults')" style="width:100px">
                                <el-option label="Top 6" value="6"></el-option>
                                <el-option label="Top 12" value="12"></el-option>
                                <el-option label="Top 30" value="30"></el-option>
                                <el-option label="Top 150" value="150"></el-option>
                                <el-option :label="$t('formPlaceholders.topnAll')" value="1000000"></el-option>
                            </el-select>
                            <el-button type="success" icon="Search" @click="search(0)">{{ $t('searchButtons.search') }}</el-button>
                        </el-col>
                    </el-row>
                    <el-collapse v-model="activeCollapse">
                        <el-collapse-item :title="$t('formPlaceholders.advanceSearch')" name="1" icon="CaretRight">
                            <el-input :placeholder="$t('formPlaceholders.negativeSearch')" v-model="form.negative" class="input-with-select"
                              @keyup.enter.native="search(0)"></el-input>
                            <el-input :placeholder="$t('formPlaceholders.path')" v-model="form.path" class="input-with-select"
                                      @keyup.enter.native="search(0)"></el-input>
                            <el-form-item :label="$t('formPlaceholders.positiveThreshold')" style="margin-bottom: 0px;" @keyup.enter.native="search(0)">
                                <el-slider style="width:200px" v-model="form.positive_threshold"></el-slider>
                            </el-form-item>
                            <el-form-item :label="$t('formPlaceholders.negativeThreshold')" style="margin-bottom: 0px;" @keyup.enter.native="search(0)">
                                <el-slider style="width:200px" v-model="form.negative_threshold"></el-slider>
                            </el-form-item>
                            <el-form-item :label="$t('formPlaceholders.date')" style="margin-bottom: 0px;">
                                <el-date-picker v-model="time_filter" type="datetimerange" start-placeholder="Start date" end-placeholder="End date"
                                                format="YYYY-MM-DD HH:mm:ss" date-format="YYYY/MM/DD ddd" time-format="A hh:mm:ss"></el-date-picker>
                            </el-form-item>
                        </el-collapse-item>
                    </el-collapse>
                </el-form>
            </el-tab-pane>

            <el-tab-pane :label="$t('searchTabs.imageSearch')" name="1">
                <el-row>
                    <el-col :span="6" style="display: flex; justify-content: center; padding-right: 10px">
                        <el-image fit="contain" :src="imageSearchUrl"></el-image>
                    </el-col>
                    <el-col :span="18">
                        <el-upload ref="upload" drag action="api/upload" :multiple=false :limit="1" :on-exceed="handleExceed" :on-success="handleSuccess" :before-upload="handleBeforeUpload">
                            <i class="el-icon-upload"></i>
                            <div class="el-upload__text">{{ $t('uploader.drag') }}<em>{{ $t('uploader.click') }}</em></div>
                        </el-upload>
                        <el-form ref="form" :model="form" :inline="true" style="margin-top:5px;">
                            <el-select v-model="form.top_n" :placeholder="$t('formPlaceholders.topNResults')" style="width:100px"
                                       @keyup.enter.native="search(1)">
                                <el-option label="Top 6" value="6"></el-option>
                                <el-option label="Top 12" value="12"></el-option>
                                <el-option label="Top 30" value="30"></el-option>
                                <el-option label="Top 150" value="150"></el-option>
                                <el-option :label="$t('formPlaceholders.topnAll')" value="1000000"></el-option>
                            </el-select>
                            <el-button type="success" icon="Search" @click="search(1)">{{ $t('searchButtons.search') }}</el-button>
                            <el-button type="success" icon="Pointer" @click="pasteImg">{{ $t('searchButtons.paste') }}</el-button>
                            <el-collapse v-model="activeCollapse">
                                <el-collapse-item :title="$t('formPlaceholders.advanceSearch')" name="1" icon="CaretRight">
                                    <el-input :placeholder="$t('formPlaceholders.path')" v-model="form.path" class="input-with-select"
                                              @keyup.enter.native="search(1)"></el-input>
                                    <el-form-item :label="$t('formPlaceholders.positiveThreshold')" style="margin-bottom: 0px;" @keyup.enter.native="search(1)">
                                        <el-slider style="width:200px" v-model="form.image_threshold"></el-slider>
                                    </el-form-item>
                                    <el-form-item :label="$t('formPlaceholders.date')" style="margin-bottom: 0px;">
                                        <el-date-picker v-model="time_filter" type="datetimerange" start-placeholder="Start date" end-placeholder="End date"
                                                        format="YYYY-MM-DD HH:mm:ss" date-format="YYYY/MM/DD ddd" time-format="A hh:mm:ss"></el-date-picker>
                                    </el-form-item>
                                </el-collapse-item>
                            </el-collapse>
                        </el-form>
                    </el-col>
                </el-row>
            </el-tab-pane>

            <el-tab-pane :label="$t('searchTabs.textVideoSearch')" name="2">
                <el-form ref="form" :model="form" :inline="true">
                    <el-row>
                        <el-col :span="18">
                            <el-input :placeholder="$t('formPlaceholders.positiveSearch')" v-model="form.positive" class="input-with-select"
                              @keyup.enter.native="search(2)"></el-input>
                        </el-col>
                        <el-col :span="6">
                            <el-select v-model="form.top_n" :placeholder="$t('formPlaceholders.topNResults')" style="width:100px">
                                <el-option label="Top 6" value="6"></el-option>
                                <el-option label="Top 12" value="12"></el-option>
                                <el-option label="Top 30" value="30"></el-option>
                                <el-option label="Top 150" value="150"></el-option>
                                <el-option :label="$t('formPlaceholders.topnAll')" value="1000000"></el-option>
                            </el-select>
                            <el-button type="success" icon="Search" @click="search(2)">{{ $t('searchButtons.search') }}</el-button>
                        </el-col>
                    </el-row>
                    <el-collapse v-model="activeCollapse">
                        <el-collapse-item :title="$t('formPlaceholders.advanceSearch')" name="1" icon="CaretRight">
                            <el-input :placeholder="$t('formPlaceholders.negativeSearch')" v-model="form.negative" class="input-with-select"
                                      @keyup.enter.native="search(2)"></el-input>
                            <el-input :placeholder="$t('formPlaceholders.path')" v-model="form.path" class="input-with-select"
                                      @keyup.enter.native="search(2)"></el-input>
                            <el-form-item :label="$t('formPlaceholders.positiveThreshold')" style="margin-bottom: 0px;" @keyup.enter.native="search(2)">
                                <el-slider style="width:200px" v-model="form.positive_threshold"></el-slider>
                            </el-form-item>
                            <el-form-item :label="$t('formPlaceholders.negativeThreshold')" style="margin-bottom: 0px;" @keyup.enter.native="search(2)">
                                <el-slider style="width:200px" v-model="form.negative_threshold"></el-slider>
                            </el-form-item>
                            <el-form-item :label="$t('formPlaceholders.date')" style="margin-bottom: 0px;">
                                <el-date-picker v-model="time_filter" type="datetimerange" start-placeholder="Start date" end-placeholder="End date"
                                                format="YYYY-MM-DD HH:mm:ss" date-format="YYYY/MM/DD ddd" time-format="A hh:mm:ss"></el-date-picker>
                            </el-form-item>
                        </el-collapse-item>
                    </el-collapse>
                </el-form>
            </el-tab-pane>

            <el-tab-pane :label="$t('searchTabs.imageVideoSearch')" name="3">
                <el-row>
                    <el-col :span="6" style="display: flex; justify-content: center; padding-right: 10px">
                        <el-image fit="contain" :src="imageSearchUrl"></el-image>
                    </el-col>
                    <el-col :span="18">
                        <el-upload ref="upload" drag action="api/upload" :multiple=false :limit="1" :on-exceed="handleExceed" :on-success="handleSuccess" :before-upload="handleBeforeUpload">
                            <i class="el-icon-upload"></i>
                            <div class="el-upload__text">{{ $t('uploader.drag') }}<em>{{ $t('uploader.click') }}</em></div>
                        </el-upload>
                        <el-form ref="form" :model="form" :inline="true" style="margin-top:5px;">
                            <el-select v-model="form.top_n" :placeholder="$t('formPlaceholders.topNResults')" style="width:100px"
                                       @keyup.enter.native="search(1)">
                                <el-option label="Top 6" value="6"></el-option>
                                <el-option label="Top 12" value="12"></el-option>
                                <el-option label="Top 30" value="30"></el-option>
                                <el-option label="Top 150" value="150"></el-option>
                                <el-option :label="$t('formPlaceholders.topnAll')" value="1000000"></el-option>
                            </el-select>
                            <el-button type="success" icon="Search" @click="search(3)">{{ $t('searchButtons.search') }}</el-button>
                            <el-button type="success" icon="Pointer" @click="pasteImg">{{ $t('searchButtons.paste') }}</el-button>
                            <el-collapse v-model="activeCollapse">
                                <el-collapse-item :title="$t('formPlaceholders.advanceSearch')" name="1" icon="CaretRight">
                                    <el-input :placeholder="$t('formPlaceholders.path')" v-model="form.path" class="input-with-select"
                                              @keyup.enter.native="search(3)"></el-input>
                                    <el-form-item :label="$t('formPlaceholders.positiveThreshold')" style="margin-bottom: 0px;" @keyup.enter.native="search(1)">
                                        <el-slider style="width:200px" v-model="form.image_threshold"></el-slider>
                                    </el-form-item>
                                    <el-form-item :label="$t('formPlaceholders.date')" style="margin-bottom: 0px;">
                                        <el-date-picker v-model="time_filter" type="datetimerange" start-placeholder="Start date" end-placeholder="End date"
                                                        format="YYYY-MM-DD HH:mm:ss" date-format="YYYY/MM/DD ddd" time-format="A hh:mm:ss"></el-date-picker>
                                    </el-form-item>
                                </el-collapse-item>
                            </el-collapse>
                        </el-form>
                    </el-col>
                </el-row>
            </el-tab-pane>

            <el-tab-pane :label="$t('searchTabs.textImageSimilarity')" name="6">
                <el-input :placeholder="$t('formPlaceholders.textMatch')" v-model="form.positive" class="input-with-select"></el-input>
                <el-row>
                    <el-col :span="6" style="display: flex; justify-content: center; padding-right: 10px">
                        <el-image fit="contain" :src="imageSearchUrl"></el-image>
                    </el-col>
                    <el-col :span="18">
                        <el-upload ref="upload" drag action="api/upload" :multiple=false :limit="1" :on-exceed="handleExceed" :on-success="handleSuccess" :before-upload="handleBeforeUpload">
                            <i class="el-icon-upload"></i>
                            <div class="el-upload__text">{{ $t('uploader.drag') }}<em>{{ $t('uploader.click') }}</em></div>
                        </el-upload>
                        <el-row style="margin-top:10px;">
                            <el-button type="success" icon="Search" @click="search(4)">{{ $t('searchButtons.calculateSimilarity') }}</el-button>
                            <el-button type="success" icon="Pointer" @click="pasteImg">{{ $t('searchButtons.paste') }}</el-button>
                        </el-row>
                    </el-col>
                </el-row>
            </el-tab-pane>

            <el-tab-pane v-if="status.total_pexels_videos != 0" :label="$t('searchTabs.pexelsVideos')" name="7">
                <el-form ref="form" :model="form" :inline="true">
                    <el-input :placeholder="$t('formPlaceholders.positiveSearch')" v-model="form.positive" class="input-with-select"
                              @keyup.enter.native="search(9)"></el-input>
                    <el-form-item :label="$t('formPlaceholders.positiveThreshold')" style="margin-bottom: 0px;" @keyup.enter.native="search(9)">
                        <el-slider style="width:200px" v-model="form.positive_threshold"></el-slider>
                    </el-form-item>
                    <el-select v-model="form.top_n" :placeholder="$t('formPlaceholders.topNResults')" style="width:100px">
                        <el-option label="Top 6" value="6"></el-option>
                        <el-option label="Top 12" value="12"></el-option>
                        <el-option label="Top 30" value="30"></el-option>
                        <el-option label="Top 150" value="150"></el-option>
                        <el-option :label="$t('formPlaceholders.topnAll')" value="1000000"></el-option>
                    </el-select>
                    <el-button type="success" icon="Search" @click="search(9)">{{ $t('searchButtons.search') }}</el-button>
                </el-form>
            </el-tab-pane>

        </el-tabs>
    </el-row>

    <!--TODO: 代码优化，播放量显示优化（数字过大的时候看不清位数）-->
    <el-row :gutter="5"
            v-if="form.search_type === 0 || form.search_type === 1 || form.search_type === 2 || form.search_type === 3 || form.search_type === 5 || form.search_type === 6 || form.search_type === 7 || form.search_type === 8">
        <el-col :span="8" v-for="(file, index) in files">
            <el-card style="margin-top: 5px;">
                <el-row>
                    <el-image loading="lazy"
                              v-if="form.search_type === 0 || form.search_type === 1 || form.search_type === 5 || form.search_type === 7"
                              fit="contain" :src="file.url + '?thumbnail=1'" :preview-src-list="image_url_list" :initial-index="index"
                              :hide-on-click-modal="true"></el-image>
                    <video preload="metadata"
                           v-if="form.search_type === 2 || form.search_type === 3 || form.search_type === 6 || form.search_type === 8"
                           :src="file.url" controls></video>
                </el-row>
                <el-row justify="center">
                    <el-tooltip :content="$t('fileResults.matchingProbability')" placement="bottom" v-if="file.score">
                        <el-tag size="default" effect="dark">{{(file.score * 100).toFixed(1)}}%</el-tag>
                    </el-tooltip>
                    <el-tooltip :content="file.path" placement="bottom">
                        <el-tag size="default" type="info" effect="dark" class="copy" :data-clipboard-text="file.path">{{file.path.replace(/\\/g, '/').split('/').pop()}}
                        </el-tag>
                    </el-tooltip>
                    <el-tooltip v-if="file.end_time" :content="$t('fileResults.matchingTimeRange')" placement="bottom">
                        <el-tag size="default" type="warning" effect="dark">{{file.start_time}} ~ {{file.end_time}}</el-tag>
                    </el-tooltip>
                    <el-button size="small" round type="success"
                               v-if="form.search_type === 0 || form.search_type === 1 || form.search_type === 5 || form.search_type === 7"
                               @click="searchFromImage(5,file.url)">{{ $t('fileResults.imageSearch') }}
                    </el-button>
                    <el-button size="small" round type="success"
                               v-if="form.search_type === 0 || form.search_type === 1 || form.search_type === 5 || form.search_type === 7"
                               @click="searchFromImage(6,file.url)">{{ $t('fileResults.imageVideoSearch') }}
                    </el-button>
                    <el-button size="small" round type="success" v-if="form.search_type === 2 || form.search_type === 3 || form.search_type === 6"
                               @click="downloadVideoClip(file.url,file.start_time,file.end_time)">{{ $t('fileResults.downloadVideoClip') }}
                    </el-button>
                </el-row>
            </el-card>
        </el-col>
    </el-row>

    <el-row :gutter="5" v-if="form.search_type === 9">
        <el-row :gutter="5">
            <el-col :span="8" v-for="video in pexels_videos">
                <el-card style="margin-top: 5px;">
                    <el-row style="text-align: center"><strong>{{video.title}}</strong></el-row>
                    <video preload="none" :src="video.content_loc" :poster="video.thumbnail_loc + '?fm=webp&fit=corp&min-w=640&h=480'"
                           controls></video>
                    <el-row>
                        <el-tooltip :content="$t('fileResults.matchingProbability')" placement="bottom" v-if="video.score">
                            <el-tag size="small" effect="dark">{{(video.score * 100).toFixed(1)}}%
                            </el-tag>
                        </el-tooltip>
                        <el-tag size="small" type="success" effect="dark">{{ $t('pexelsResults.viewCount') }}: {{video.view_count}}</el-tag>
                        <el-link :href="'https://www.pexels.com/video/' + video.thumbnail_loc.split('/')[4]" type="info" target="_blank">
                            {{ $t('pexelsResults.sourcePage') }}
                        </el-link>
                    </el-row>
                    <el-row>{{video.description}}</el-row>
                </el-card>
            </el-col>
        </el-row>
    </el-row>

    <el-footer style="text-align: center; color: gray; padding: 10px 0; font-size: 14px;">
        <span style="vertical-align: middle;">{{ $t('footer.description1') }}</span>
        <el-link style="vertical-align: middle;" href="https://github.com/chn-lee-yumi/MaterialSearch/" type="info" target="_blank">
            https://github.com/chn-lee-yumi/MaterialSearch/
        </el-link>
        <span style="vertical-align: middle;">{{ $t('footer.description2') }}</span>
    </el-footer>
</el-main>
</body>
<script>
const app = Vue.createApp({
    data() {
        return {
            currentTab: "0",
            isScanning: false,
            status: {
                total_images: 0,
                total_pexels_videos: 0,
                total_video_frames: 0,
                total_videos: 0,
            },
            enableLogin: null,
            form: {
                positive: '',
                negative: '',
                top_n: '6',
                search_type: 0,
                positive_threshold: 20,
                negative_threshold: 30,
                image_threshold: 75,
                img_id: -1,
                path: '',
                start_time: 0,
                end_time: 0,
            },
            time_filter: null,
            files: [],
            image_url_list: [],
            pexels_videos: [],
            timer: null,
            imageSearchUrl: null,
            activeCollapse: [],
        }
    },
    watch: {
        'currentTab'(val) { localStorage.setItem('currentTab', val); },
        'form.top_n'(val) { localStorage.setItem('form.top_n', val); },
        'form.positive_threshold'(val) { localStorage.setItem('form.positive_threshold', val); },
        'form.negative_threshold'(val) { localStorage.setItem('form.negative_threshold', val); },
        'form.image_threshold'(val) { localStorage.setItem('form.image_threshold', val); },
    },
    mounted: function() {
        fetchServerTimeAndSetOffset();  // 页面加载时获取服务器时间
        this.loadData();
        this.timer = setInterval(this.loadData, 5000);
        // 加载保存的表单设置
        this.currentTab = localStorage.getItem('currentTab') || '0';
        const keys = ['form.top_n', 'form.positive_threshold',  'form.negative_threshold', 'form.image_threshold'];
        keys.forEach(k => {
            const val = localStorage.getItem(k);
            if (val !== null) {
                const field = k.split('.')[1];
                this.form[field] = field.includes('threshold') ? Number(val) : val;
            }
        });
        // 添加全局粘贴事件监听，实现 Ctrl+V 粘贴图片
        window.addEventListener('paste', async (event) => {
            console.log(event)
            if (!["1", "3"].includes(this.currentTab)) return; // 6不做自动粘贴，因为会覆盖文本框粘贴功能
            await this.pasteImg();
        });
    },
    created() {
        var that = this
        let clipboard = new ClipboardJS(".copy");
        clipboard.on("success", function(e) {
            ElementPlus.ElMessage.success(that.$t('messages.clipboardCopySuccess'));
            e.clearSelection();
        })
    },
    methods: {
        changeLocale(locale) {
            this.$i18n.locale = locale
        },
        cleanCache() {
            var that = this;
            axios.get('api/clean_cache')
                .then(function(response) {
                    ElementPlus.ElMessage.info(response.data);
                })
                .catch(function(error) {
                    console.log(error);
                    ElementPlus.ElMessage.error(error.response.data);
                })
        },
        downloadVideoClip(url, start_time, end_time) {
            var parts = url.split('/');
            var lastPart = parts[parts.length - 1];
            var hashPart = lastPart.split('#')[0];
            window.open(`/api/download_video_clip/${hashPart}/${start_time}/${end_time}`, "_blank");
        },
        loadData() {
            var that = this;
            axios.get('api/status')
                .then(function(response) {
                    console.log(response);
                    that.status = response["data"];
                    that.status.remain_time = formatTime(that.status.remain_time);
                    that.isScanning = response["data"]["status"];
                    that.enableLogin = response["data"]["enable_login"];
                })
                .catch(function(error) {
                    console.log(error);
                    ElementPlus.ElMessage.error(error);
                })
        },
        scan() {
            var that = this;
            axios.get('api/scan')
                .then(function(response) {
                    console.log(response);
                    that.loadData();
                })
                .catch(function(error) {
                    console.log(error);
                    ElementPlus.ElMessage.error(error.response.data);
                })
        },
        search(search_type) {
            var that = this;
            var loadingInstance;
            if (search_type === 4) {
                loadingInstance = ElementPlus.ElLoading.service({ fullscreen: true, text: that.$t('messages.matching') });
            } else {
                loadingInstance = ElementPlus.ElLoading.service({ fullscreen: true, text: that.$t('messages.searching') });
            }
            this.upload.clearFiles();
            that.form.search_type = search_type;
            if ((search_type === 0 || search_type === 2) && that.form.positive == "" && that.form.path == "") {
                ElementPlus.ElMessage.error(that.$t('messages.searchContentEmpty'));
                loadingInstance.close();
                return;
            }
            if ((search_type === 4 || search_type == 9) && that.form.positive == "") {
                ElementPlus.ElMessage.error(that.$t('messages.textContentEmpty'));
                loadingInstance.close();
                return;
            }
            if (that.time_filter) {
                that.form.start_time = Math.floor(that.time_filter[0] / 1000);
                that.form.end_time = Math.floor(that.time_filter[1] / 1000);
            } else {
                that.form.start_time = null;
                that.form.end_time = null;
            }
            axios.post('/api/match', that.form)
                .then(function(response) {
                    console.log(response);
                    loadingInstance.close();
                    if (search_type === 0 || search_type === 1 || search_type === 5) {
                        that.files = response["data"];
                        that.image_url_list = [];
                        that.files.forEach(function(element) {
                            that.image_url_list.push(element.url);
                        });
                        ElementPlus.ElMessage.info(that.$t('messages.totalSearchResult') + that.files.length + that.$t('messages.photos'));
                        return;
                    }
                    if (search_type === 2 || search_type === 3 || search_type === 6) {
                        that.files = response["data"];
                        ElementPlus.ElMessage.info(that.$t('messages.totalSearchResult') + that.files.length + that.$t('messages.videos'));
                        return;
                    }
                    if (search_type === 9) {
                        that.pexels_videos = response["data"];
                        return;
                    }
                    if (search_type === 4) {
                        ElementPlus.ElMessage.info(that.$t('messages.matchingSimilarityInfo') + response["data"]["score"] + "%");
                        return;
                    }
                })
                .catch(function(error) {
                    console.log(error);
                    loadingInstance.close();
                    ElementPlus.ElMessage.error(error.response.data);
                })
        },
        searchFromImage(search_type, img_url) {
            console.log(this.currentTab);
            if (search_type === 5) {
                this.currentTab = "1";
            } else if (search_type === 6) {
                this.currentTab = "3";
            }
            this.form.search_type = search_type;
            this.form.positive = '';
            this.form.negative = '';
            var id_str = img_url.match(/(\d+)$/);
            if (id_str) {
                var id_num = parseInt(id_str[1], 10);
                this.form.img_id = id_num;
                this.search(search_type);
            } else {
                ElementPlus.ElMessage.error(that.$t('messages.imgIdNotFound'));
            }
        },
        handleBeforeUpload(file) {
            console.log(file);
            return new Promise((resolve) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.imageSearchUrl = e.target.result;
                    resolve(true);
                };
                reader.readAsDataURL(file);
            });
        },
        async pasteImg() {
            // 检查浏览器是否支持剪切板 API 和 Image 对象
            if (navigator.clipboard && window.Image) {
                try {
                    // 读取剪切板中的内容，返回一个 Promise，解析为一个 ClipboardItem 数组
                    const clipboardItems = await navigator.clipboard.read();
                    // 检查剪切板中是否有内容，并且第一个项目是否包含图片类型（PNG 或 JPEG）
                    if (clipboardItems.length > 0 && (clipboardItems[0].types.includes('image/png') || clipboardItems[0].types.includes('image/jpeg'))) {
                        // 获取剪切板中的第一个图片项目
                        const [imageItem] = clipboardItems;
                        // 获取图片的 Blob 数据
                        const blob = await imageItem.getType('image/png');
                        // 将 Blob 对象转换为 File 对象
                        const file = this.blobToFile(blob, 'pasteboard.png');
                        // 清空当前文件列表
                        this.upload.clearFiles();
                        // 开始处理文件
                        this.upload.handleStart(file);
                        // 提交文件上传请求
                        this.upload.submit();
                    } else {
                        ElementPlus.ElMessage.error(this.$t('剪切板没有图片'));
                    }
                } catch (error) {
                    // 捕获并处理可能的错误
                    console.error('读取剪切板失败:', error);
                    ElementPlus.ElMessage.error(this.$t('messages.clipboardReadFailed'));
                }
            } else {
                ElementPlus.ElMessage.error(this.$t('messages.clipboardNotSupported'));
            }
        },
        blobToFile(blob, fileName) {
            return new File([blob], fileName, { type: blob.type });
        },
    },
    setup() {
        const upload = Vue.ref();
        const { t } = VueI18n.useI18n({ useScope: "global" });
        function handleExceed(files, fileList) {
            upload.value.clearFiles();
            upload.value.handleStart(files[0]);
            upload.value.submit();
        }
        function handleSuccess() {
            ElementPlus.ElMessage.success(t('messages.uploadSuccess'));
        }
        return {
            upload,
            handleExceed,
            handleSuccess,
        };
    },
})
</script>
<style scoped>
.el-tag {
    margin-right: 2px;
}
.el-select {
    margin-right: 2px;
    padding-top: 2px; // 不加这个会发现select和button对不齐
}
.el-tabs {
    margin-top: 5px;
    width: 100%;
}
.el-input {
    margin-bottom: 2px;
}
.el-upload, .el-upload-dragger {
    width: 100% !important;
}
.el-button {
    border: 0px;
}
.el-button+.el-button {
    margin-left: 2px;
}
.el-upload-list__item:first-child {
    margin-top: 0px;
}
.el-tabs--border-card>.el-tabs__content {
    padding: 10px 15px;
}
.el-card__body {
    padding: 10px;
}
.el-image {
    width: 100%;
    max-height: 240px;
    margin-bottom: 2px;
}
video {
    width: 100%;
    max-height: 240px;
    margin-bottom: 2px;
}
</style>
</html>
