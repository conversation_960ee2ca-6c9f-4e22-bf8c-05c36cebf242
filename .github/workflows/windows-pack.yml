name: Windows Pack

on:
  release:
    types: [created]

env:
  HF_HOME: MaterialSearchWindows/huggingface

jobs:
  build:
    runs-on: windows-latest
    steps:

    - name: Checkout
      uses: actions/checkout@v4
      with:
        path: MaterialSearchWindows

    - name: Download Python
      run: Invoke-WebRequest "https://www.python.org/ftp/python/3.12.9/python-3.12.9-embed-amd64.zip" -OutFile python.zip

    - name: Unzip Python
      run: Expand-Archive python.zip -DestinationPath MaterialSearchWindows

    - name: Fix python312._pth
      uses: DamianReeves/write-file-action@master
      with:
        path: MaterialSearchWindows/python312._pth
        write-mode: append
        contents: |
          import site

    - name: Download FFMpeg
      run: Invoke-WebRequest "https://github.com/GyanD/codexffmpeg/releases/download/7.1.1/ffmpeg-7.1.1-full_build.zip" -OutFile ffmpeg.zip

    - name: Unzip FFMpeg
      run: Expand-Archive ffmpeg.zip -DestinationPath .

    - name: Copy FFMpeg
      run: cp ffmpeg-7.1.1-full_build/bin/ffmpeg.exe MaterialSearchWindows

    - name: Download pip
      run: Invoke-WebRequest "https://bootstrap.pypa.io/pip/pip.pyz" -OutFile MaterialSearchWindows/pip.pyz

    - name: Optimise Code
      run: |
        cd MaterialSearchWindows
        ./python.exe -m py_compile main.py database.py init.py routes.py models.py process_assets.py scan.py search.py utils.py
        cp __pycache__/main.cpython-312.pyc main.pyc
        cp __pycache__/database.cpython-312.pyc database.pyc
        cp __pycache__/init.cpython-312.pyc init.pyc
        cp __pycache__/routes.cpython-312.pyc routes.pyc
        cp __pycache__/models.cpython-312.pyc models.pyc
        cp __pycache__/process_assets.cpython-312.pyc process_assets.pyc
        cp __pycache__/scan.cpython-312.pyc scan.pyc
        cp __pycache__/search.cpython-312.pyc search.pyc
        cp __pycache__/utils.cpython-312.pyc utils.pyc
        rm main.py
        rm database.py
        rm init.py
        rm routes.py
        rm models.py
        rm process_assets.py
        rm scan.py
        rm search.py
        rm utils.py
        rm -r __pycache__
        cd ..

    - name: Install requirements
      run: MaterialSearchWindows/python.exe MaterialSearchWindows/pip.pyz install -r MaterialSearchWindows/requirements_windows.txt

    - name: Download model
      run: MaterialSearchWindows/python.exe -c "from transformers import AutoModelForZeroShotImageClassification, AutoProcessor; AutoModelForZeroShotImageClassification.from_pretrained('OFA-Sys/chinese-clip-vit-base-patch16', use_safetensors=False); AutoProcessor.from_pretrained('OFA-Sys/chinese-clip-vit-base-patch16');"
      
    - name: Create .env
      uses: DamianReeves/write-file-action@master
      with:
        path: MaterialSearchWindows/.env
        write-mode: overwrite
        contents: |
          # 下面添加扫描路径，用逗号分隔
          ASSETS_PATH=C:\Users\<USER>\Pictures,C:\Users\<USER>\Videos
          # 如果路径或文件名包含这些字符串，就跳过，逗号分隔，不区分大小写
          IGNORE_STRINGS=thumb,avatar,__MACOSX,icons,cache
          # 图片最小宽度，小于此宽度则忽略。不需要可以改成0
          IMAGE_MIN_WIDTH=64
          # 图片最小高度，小于此高度则忽略。不需要可以改成0。
          IMAGE_MIN_HEIGHT=64
          # 视频每隔多少秒取一帧，视频展示的时候，间隔小于等于2倍FRAME_INTERVAL的算为同一个素材，同时开始时间和结束时间各延长0.5个FRAME_INTERVAL，要求为整数，最小为1
          FRAME_INTERVAL=2
          # 视频搜索出来的片段前后延长时间，单位秒，如果搜索出来的片段不完整，可以调大这个值
          VIDEO_EXTENSION_LENGTH=1
          # 支持的图片拓展名，逗号分隔，请填小写
          IMAGE_EXTENSIONS=.jpg,.jpeg,.png,.gif,.heic,.webp,.bmp
          # 支持的视频拓展名，逗号分隔，请填小写
          VIDEO_EXTENSIONS=.mp4,.flv,.mov,.mkv,.webm,.avi
          # 监听IP，如果想允许远程访问，把这个改成0.0.0.0
          HOST=127.0.0.1
          # 监听端口
          PORT=8085
          # 运行模型的设备，默认自动选择(auto)。如果想强制使用cpu运行，则把值改成cpu。
          DEVICE=auto
          # 下面的不要改
          TRANSFORMERS_OFFLINE=1
          HF_HUB_OFFLINE=1
          HF_HOME=huggingface

    - name: Create run.bat
      uses: DamianReeves/write-file-action@master
      with:
        path: MaterialSearchWindows/run.bat
        write-mode: overwrite
        contents: |
          .\python.exe main.pyc
          PAUSE
          
    - name: Create 使用说明.txt
      uses: DamianReeves/write-file-action@master
      with:
        path: MaterialSearchWindows/使用说明.txt
        write-mode: overwrite
        contents: |
          右键“.env”文件进行编辑，配置扫描路径和设备，然后保存。
          最后双击运行“run.bat”即可，待看到"http://127.0.0.1:8085"的输出就可以浏览器打开对应链接进行使用。
          关闭“run.bat”的运行框即关闭程序。
          本软件是开源软件，免费下载使用，不用付款购买，切勿上当受骗！
          最新版本下载和详细使用说明请看：https://github.com/chn-lee-yumi/MaterialSearch

    - name: Download 7zr
      run: Invoke-WebRequest "https://www.7-zip.org/a/7zr.exe" -OutFile 7zr.exe

    - name: Compress (has bug)
      run: Compress-Archive -CompressionLevel NoCompression -LiteralPath MaterialSearchWindows -DestinationPath MaterialSearchWindows.zip

    # 下载模型的时候snapshot是链接到blobs的，但压缩的时候会将blobs和snapshot都压缩一次，导致解压后模型变成双倍大小。另外新版本huggingface会下载model.safetensors，多占一倍空间，因此要删掉。
    - name: Unzip (solve zip issue)
      run: |
        Expand-Archive MaterialSearchWindows.zip -DestinationPath MaterialSearch_tmp
        rm MaterialSearchWindows.zip
        rm -r MaterialSearch_tmp/MaterialSearchWindows/huggingface/hub/models--OFA-Sys--chinese-clip-vit-base-patch16/blobs
        Get-ChildItem -Recurse -Filter "model.safetensors" | Remove-Item -Force

    # 改用7z压缩，提高压缩率
    - name: Compress (solve zip issue)
      run: cd MaterialSearch_tmp; ../7zr.exe a ../MaterialSearchWindows.7z MaterialSearchWindows

    - name: Release
      uses: softprops/action-gh-release@v2
      if: startsWith(github.ref, 'refs/tags/')
      with:
        files: MaterialSearchWindows.7z


