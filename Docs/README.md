# MaterialSearch 项目文档

基于AI的本地媒体搜索系统技术文档集合

## 文档结构

### 核心文档
- [项目概述](./01-项目概述.md) - 项目介绍、功能特性、技术选型
- [系统架构](./02-系统架构.md) - 整体架构设计、核心模块说明
- [数据库设计](./03-数据库设计.md) - PostgreSQL + pgvector + Tortoise ORM设计
- [API接口设计](./04-API接口设计.md) - 统一响应格式、接口规范、错误处理

### 功能设计
- [扩展属性系统](./05-扩展属性系统.md) - 灵活的JSON元数据管理
- [任务处理系统](./06-任务处理系统.md) - 同步异步处理、防重复机制
- [模型管理系统](./07-模型管理系统.md) - 多后端支持、动态切换
- [日志管理系统](./08-日志管理系统.md) - 统一日志管理、分级输出

### 性能优化
- [搜索性能优化](./09-搜索性能优化.md) - 快速匹配、缓存策略、批量处理
- [系统性能调优](./10-系统性能调优.md) - 硬件配置、软件优化、监控告警

### 部署运维
- [部署指南](./11-部署指南.md) - 源码部署、Docker部署
- [配置管理](./12-配置管理.md) - 环境变量、配置参数说明

### 开发指南
- [快速开始指南](./18-快速开始指南.md) - 快速部署和使用指南
- [FastAPI迁移指南](./13-FastAPI迁移指南.md) - 从Flask到FastAPI的迁移方案
- [前端界面设计](./16-前端界面设计.md) - 基于FastAPI的现代化界面设计

## 快速开始

**新用户推荐路径**：
1. 阅读 [快速开始指南](./18-快速开始指南.md) 快速上手
2. 查看 [项目概述](./01-项目概述.md) 了解完整功能
3. 参考 [搜索性能优化](./09-搜索性能优化.md) 提升性能

**开发者路径**：
1. 阅读 [系统架构](./02-系统架构.md) 理解整体设计
2. 查看 [数据库设计](./03-数据库设计.md) 了解数据结构
3. 参考 [API接口设计](./04-API接口设计.md) 进行开发
4. 根据需要查阅具体功能模块的详细文档

## 实际可用文档

目前已完成的文档：
- ✅ [项目概述](./01-项目概述.md)
- ✅ [系统架构](./02-系统架构.md)
- ✅ [数据库设计](./03-数据库设计.md)
- ✅ [API接口设计](./04-API接口设计.md)
- ✅ [扩展属性系统](./05-扩展属性系统.md)
- ✅ [任务处理系统](./06-任务处理系统.md)
- ✅ [模型管理系统](./07-模型管理系统.md)
- ✅ [日志管理系统](./08-日志管理系统.md)
- ✅ [搜索性能优化](./09-搜索性能优化.md)
- ✅ [系统性能调优](./10-系统性能调优.md)
- ✅ [部署指南](./11-部署指南.md)
- ✅ [配置管理](./12-配置管理.md)
- ✅ [前端界面设计](./16-前端界面设计.md)
- ✅ [快速开始指南](./18-快速开始指南.md)

待完成的文档：
- ⏳ [FastAPI迁移指南](./13-FastAPI迁移指南.md)
- ⏳ [运维监控](./14-运维监控.md)
- ⏳ [开发规范](./15-开发规范.md)

## 设计原则

本项目文档遵循以下设计原则：
- **简洁实用**：避免过度设计，专注核心AI搜索功能
- **性能优先**：重点关注搜索性能和用户体验优化
- **易于部署**：简化架构，减少外部依赖，降低部署复杂度
- **功能完整**：覆盖图片/视频搜索的核心业务需求

## 设计模式应用

系统采用多种设计模式提高代码质量：
- **工厂模式**：推理服务工厂，支持Transformers和ONNX后端切换
- **单例模式**：模型管理器和配置管理器，确保资源唯一性
- **策略模式**：CPU优化策略，针对不同硬件环境的优化
- **观察者模式**：任务状态通知，松耦合的事件处理
- **建造者模式**：任务构建器，简化复杂任务对象的创建

## 简化改进

相比初始设计，我们进行了以下简化：
- **移除复杂权限系统**：去掉用户认证和RBAC权限控制
- **简化数据库设计**：优化表结构，减少不必要的枚举和索引
- **精简模块架构**：合并相关功能模块，减少代码复杂度
- **减少外部依赖**：移除Redis等非必需组件
- **CPU环境优化**：专门针对CPU环境进行性能优化
- **专注核心功能**：突出AI搜索能力，简化配置管理

## 文档维护

- 文档版本：v2.1
- 最后更新：2024-12-31
- 维护者：开发团队
- 设计理念：简洁、实用、高性能
- 拆分状态：已完成核心文档拆分，剩余文档可根据需要继续完善

## 贡献指南

欢迎提交文档改进建议和补充内容，请遵循以下原则：
- 保持文档结构清晰
- 内容准确且易于理解
- 及时更新过时信息
- 添加必要的示例和说明
