# === Notice / 注意 ===
# The following code has been intentionally obfuscated to prevent the removal or tampering of copyright and attribution information.
# 下列代码已故意混淆，以防止版权和署名信息被删除或篡改。
#
# This is NOT intended to limit legitimate use of this open-source project under its license.
# 此举并非为了限制用户在遵循开源许可证前提下的合法使用。
#
# Please do not attempt to bypass or modify this section to remove copyright or attribution.
# 请勿尝试绕过或修改此部分代码以移除版权或署名信息。
#
# To ensure compliance with the license, please retain all copyright notices.
# 为遵守许可证条款，请保留所有版权声明。
#
# We appreciate your respect for the original authorship.
# 感谢您对原创作者的尊重。
import lzma, base64
exec(lzma.decompress(base64.b64decode('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')))
pre_init = OoOO00
post_init = IiIi1Ii1111
del OoOO
del O0o00
del OO0O00oo0
del OoOo
del III
del OOoOoo000O00
del Oo0Oo
del OOOo
del OoOO00