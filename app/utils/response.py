"""
统一API响应格式

提供标准化的API响应结构和错误码定义。
"""

from enum import IntEnum
from typing import Any, Optional, Generic, TypeVar
from pydantic import BaseModel, Field

T = TypeVar('T')


class ErrorCode(IntEnum):
    """错误码定义"""
    
    # 成功
    SUCCESS = 0

    # 通用错误 (1000-1999)
    UNKNOWN_ERROR = 1000
    INVALID_PARAMETER = 1001
    MISSING_PARAMETER = 1002
    INVALID_REQUEST = 1003
    PERMISSION_DENIED = 1004
    RESOURCE_NOT_FOUND = 1005
    RESOURCE_ALREADY_EXISTS = 1006
    OPERATION_FAILED = 1007
    
    # 认证授权错误 (2000-2999)
    UNAUTHORIZED = 2000
    TOKEN_EXPIRED = 2001
    TOKEN_INVALID = 2002
    ACCESS_DENIED = 2003
    
    # 数据库错误 (3000-3999)
    DATABASE_ERROR = 3000
    DATABASE_CONNECTION_FAILED = 3001
    DATABASE_QUERY_FAILED = 3002
    DATABASE_CONSTRAINT_VIOLATION = 3003
    
    # 文件处理错误 (4000-4999)
    FILE_NOT_FOUND = 4000
    FILE_UPLOAD_FAILED = 4001
    FILE_FORMAT_UNSUPPORTED = 4002
    FILE_SIZE_EXCEEDED = 4003
    FILE_PROCESSING_FAILED = 4004
    
    # 模型相关错误 (5000-5999)
    MODEL_LOAD_FAILED = 5000
    MODEL_INFERENCE_FAILED = 5001
    MODEL_NOT_FOUND = 5002
    MODEL_INITIALIZATION_FAILED = 5003
    
    # 搜索相关错误 (6000-6999)
    SEARCH_FAILED = 6000
    SEARCH_TIMEOUT = 6001
    SEARCH_INVALID_QUERY = 6002
    SEARCH_NO_RESULTS = 6003
    
    # 任务处理错误 (7000-7999)
    TASK_CREATION_FAILED = 7000
    TASK_EXECUTION_FAILED = 7001
    TASK_TIMEOUT = 7002
    TASK_CANCELLED = 7003
    TASK_NOT_FOUND = 7004
    
    # 配置错误 (8000-8999)
    CONFIG_LOAD_FAILED = 8000
    CONFIG_VALIDATION_FAILED = 8001
    CONFIG_UPDATE_FAILED = 8002
    
    # 系统错误 (9000-9999)
    SYSTEM_ERROR = 9000
    SYSTEM_OVERLOAD = 9001
    SYSTEM_MAINTENANCE = 9002
    EXTERNAL_SERVICE_ERROR = 9003

ErrorCodeEnum = ErrorCode
class ApiResponse(BaseModel, Generic[T]):
    """统一API响应格式"""
    
    ErrorCode: int = Field(description="错误码，0表示成功")
    Msg: str = Field(description="响应消息")
    IsSuccess: bool = Field(description="是否成功")
    Body: Optional[T] = Field(default=None, description="响应数据")
    
    @classmethod
    def success(cls, data: Optional[T] = None, message: str = "操作成功") -> "ApiResponse[T]":
        """创建成功响应"""
        return cls(
            ErrorCode=ErrorCodeEnum.SUCCESS,
            Msg=message,
            IsSuccess=True,
            Body=data
        )
    
    @classmethod
    def error(
        cls,
        error_code: ErrorCodeEnum = ErrorCodeEnum.UNKNOWN_ERROR,
        message: str = "操作失败",
        data: Optional[T] = None
    ) -> "ApiResponse[T]":
        """创建错误响应"""
        return cls(
            ErrorCode=error_code,
            Msg=message,
            IsSuccess=False,
            Body=data
        )
    
    @classmethod
    def from_exception(cls, exception: Exception) -> "ApiResponse[None]":
        """从异常创建错误响应"""
        from app.utils.exceptions import MaterialSearchException
        
        if isinstance(exception, MaterialSearchException):
            return cls.error(
                error_code=exception.error_code,
                message=exception.message
            )
        else:
            return cls.error(
                error_code=ErrorCodeEnum.UNKNOWN_ERROR,
                message=str(exception)
            )


class PaginationInfo(BaseModel):
    """分页信息"""
    
    page: int = Field(ge=1, description="当前页码")
    page_size: int = Field(ge=1, le=100, description="每页大小")
    total: int = Field(ge=0, description="总记录数")
    total_pages: int = Field(ge=0, description="总页数")
    has_next: bool = Field(description="是否有下一页")
    has_prev: bool = Field(description="是否有上一页")
    
    @classmethod
    def create(cls, page: int, page_size: int, total: int) -> "PaginationInfo":
        """创建分页信息"""
        total_pages = (total + page_size - 1) // page_size
        
        return cls(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )


class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应"""
    
    items: list[T] = Field(description="数据列表")
    pagination: PaginationInfo = Field(description="分页信息")


class SearchResponse(BaseModel):
    """搜索响应"""
    
    query: str = Field(description="搜索查询")
    query_type: str = Field(description="查询类型")
    total_results: int = Field(description="结果总数")
    search_time: float = Field(description="搜索耗时(秒)")
    results: list[dict] = Field(description="搜索结果")


class TaskResponse(BaseModel):
    """任务响应"""
    
    task_id: str = Field(description="任务ID")
    task_type: str = Field(description="任务类型")
    status: str = Field(description="任务状态")
    progress: float = Field(ge=0, le=100, description="进度百分比")
    message: str = Field(description="状态消息")
    created_at: str = Field(description="创建时间")
    started_at: Optional[str] = Field(default=None, description="开始时间")
    completed_at: Optional[str] = Field(default=None, description="完成时间")


class SystemStatusResponse(BaseModel):
    """系统状态响应"""
    
    status: str = Field(description="系统状态")
    version: str = Field(description="系统版本")
    uptime: float = Field(description="运行时间(秒)")
    memory_usage: dict = Field(description="内存使用情况")
    disk_usage: dict = Field(description="磁盘使用情况")
    database_status: str = Field(description="数据库状态")
    model_status: str = Field(description="模型状态")
    
    
class FileUploadResponse(BaseModel):
    """文件上传响应"""
    
    file_id: str = Field(description="文件ID")
    filename: str = Field(description="文件名")
    file_size: int = Field(description="文件大小")
    file_type: str = Field(description="文件类型")
    upload_time: str = Field(description="上传时间")
    processing_status: str = Field(description="处理状态")


# 错误码消息映射
ERROR_MESSAGES = {
    ErrorCode.SUCCESS: "操作成功",
    ErrorCode.UNKNOWN_ERROR: "未知错误",
    ErrorCode.INVALID_PARAMETER: "参数无效",
    ErrorCode.MISSING_PARAMETER: "缺少必需参数",
    ErrorCode.INVALID_REQUEST: "请求无效",
    ErrorCode.PERMISSION_DENIED: "权限不足",
    ErrorCode.RESOURCE_NOT_FOUND: "资源不存在",
    ErrorCode.RESOURCE_ALREADY_EXISTS: "资源已存在",
    ErrorCode.OPERATION_FAILED: "操作失败",
    
    ErrorCode.UNAUTHORIZED: "未授权访问",
    ErrorCode.TOKEN_EXPIRED: "令牌已过期",
    ErrorCode.TOKEN_INVALID: "令牌无效",
    ErrorCode.ACCESS_DENIED: "访问被拒绝",
    
    ErrorCode.DATABASE_ERROR: "数据库错误",
    ErrorCode.DATABASE_CONNECTION_FAILED: "数据库连接失败",
    ErrorCode.DATABASE_QUERY_FAILED: "数据库查询失败",
    ErrorCode.DATABASE_CONSTRAINT_VIOLATION: "数据库约束违反",
    
    ErrorCode.FILE_NOT_FOUND: "文件不存在",
    ErrorCode.FILE_UPLOAD_FAILED: "文件上传失败",
    ErrorCode.FILE_FORMAT_UNSUPPORTED: "不支持的文件格式",
    ErrorCode.FILE_SIZE_EXCEEDED: "文件大小超出限制",
    ErrorCode.FILE_PROCESSING_FAILED: "文件处理失败",
    
    ErrorCode.MODEL_LOAD_FAILED: "模型加载失败",
    ErrorCode.MODEL_INFERENCE_FAILED: "模型推理失败",
    ErrorCode.MODEL_NOT_FOUND: "模型不存在",
    ErrorCode.MODEL_INITIALIZATION_FAILED: "模型初始化失败",
    
    ErrorCode.SEARCH_FAILED: "搜索失败",
    ErrorCode.SEARCH_TIMEOUT: "搜索超时",
    ErrorCode.SEARCH_INVALID_QUERY: "搜索查询无效",
    ErrorCode.SEARCH_NO_RESULTS: "没有搜索结果",
    
    ErrorCode.TASK_CREATION_FAILED: "任务创建失败",
    ErrorCode.TASK_EXECUTION_FAILED: "任务执行失败",
    ErrorCode.TASK_TIMEOUT: "任务超时",
    ErrorCode.TASK_CANCELLED: "任务已取消",
    ErrorCode.TASK_NOT_FOUND: "任务不存在",
    
    ErrorCode.CONFIG_LOAD_FAILED: "配置加载失败",
    ErrorCode.CONFIG_VALIDATION_FAILED: "配置验证失败",
    ErrorCode.CONFIG_UPDATE_FAILED: "配置更新失败",
    
    ErrorCode.SYSTEM_ERROR: "系统错误",
    ErrorCode.SYSTEM_OVERLOAD: "系统过载",
    ErrorCode.SYSTEM_MAINTENANCE: "系统维护中",
    ErrorCode.EXTERNAL_SERVICE_ERROR: "外部服务错误",
}


def get_error_message(error_code: ErrorCode) -> str:
    """获取错误码对应的消息"""
    return ERROR_MESSAGES.get(error_code, "未知错误")
