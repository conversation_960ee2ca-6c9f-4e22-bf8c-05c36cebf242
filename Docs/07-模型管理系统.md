# 模型管理系统设计

## 概述

模型管理系统支持多种CLIP模型和推理后端，提供动态模型切换、性能优化和统一的模型接口。

## 支持的模型类型

### CLIP模型支持
- **中文模型**：OFA-Sys/chinese-clip-vit-base-patch16、OFA-Sys/chinese-clip-vit-large-patch14-336px
- **英文模型**：openai/clip-vit-base-patch16、openai/clip-vit-large-patch14
- **多语言模型**：laion/CLIP-ViT-B-32-multilingual-v1
- **自定义模型**：支持本地训练的CLIP模型

### 推理后端支持
- **Transformers**：Hugging Face原生推理，功能完整，易于使用
- **ONNX Runtime**：跨平台优化推理，性能提升20-50%
- **OpenVINO**：Intel硬件优化，CPU推理性能显著提升
- **TensorRT**：NVIDIA GPU优化推理（规划中）

## 模型配置管理

### 动态模型切换
- **运行时切换**：支持不重启服务切换模型
- **A/B测试**：支持同时加载多个模型进行对比
- **回退机制**：模型加载失败时自动回退到默认模型
- **预热策略**：启动时预加载模型，减少首次推理延迟

### 模型性能优化
- **精度选择**：支持FP32、FP16、INT8等不同精度
- **批处理优化**：动态调整批处理大小
- **内存管理**：模型缓存和内存释放策略
- **设备调度**：多GPU环境下的负载均衡

## 推理后端配置

### Transformers后端
- **优势**：功能完整、易于调试、社区支持好
- **适用场景**：开发测试、小规模部署、功能验证
- **配置示例**：`MODEL_BACKEND=transformers`

### ONNX后端
- **优势**：跨平台、性能优化、内存占用低
- **适用场景**：生产环境、高并发、资源受限
- **模型转换**：支持从Transformers模型自动转换
- **配置示例**：`MODEL_BACKEND=onnx`

### OpenVINO后端
- **优势**：Intel硬件优化、CPU推理性能好
- **适用场景**：Intel CPU服务器、边缘计算
- **模型优化**：支持模型量化和图优化
- **配置示例**：`MODEL_BACKEND=openvino`

## 配置参数

### 模型配置
- **MODEL_NAME**: CLIP模型名称（支持Hugging Face模型ID或本地路径）
- **MODEL_BACKEND**: 推理后端（transformers/onnx/openvino）
- **MODEL_PRECISION**: 模型精度（fp32/fp16/int8）
- **DEVICE**: 推理设备（auto/cpu/cuda/mps）
- **SCAN_PROCESS_BATCH_SIZE**: 批处理大小
- **MODEL_CACHE_DIR**: 模型缓存目录
- **ONNX_MODEL_PATH**: ONNX模型文件路径（当backend为onnx时）
- **OPENVINO_MODEL_PATH**: OpenVINO模型文件路径（当backend为openvino时）

## API接口设计

### 模型管理接口
```python
@router.get("/models", response_model=ApiResponse[ModelListData])
async def get_available_models() -> ApiResponse[ModelListData]:
    """获取可用模型列表"""
    pass

@router.get("/models/current", response_model=ApiResponse[ModelInfoData])
async def get_current_model() -> ApiResponse[ModelInfoData]:
    """获取当前使用的模型信息"""
    pass

@router.post("/models/switch", response_model=ApiResponse[None])
async def switch_model(request: SwitchModelRequest) -> ApiResponse[None]:
    """切换模型（支持热更新）"""
    pass

@router.get("/models/status", response_model=ApiResponse[ModelStatusData])
async def get_model_status() -> ApiResponse[ModelStatusData]:
    """获取模型加载状态和性能指标"""
    pass

@router.post("/models/reload", response_model=ApiResponse[None])
async def reload_model() -> ApiResponse[None]:
    """重新加载模型"""
    pass

@router.get("/models/backends", response_model=ApiResponse[BackendListData])
async def get_supported_backends() -> ApiResponse[BackendListData]:
    """获取支持的推理后端列表"""
    pass
```

## 设计模式实现

### 工厂模式 - 推理服务工厂

#### 抽象推理接口
```python
# app/core/inference/base.py
from abc import ABC, abstractmethod
from typing import List, Union, Any
import numpy as np

class InferenceBackend(ABC):
    """推理后端抽象基类"""

    @abstractmethod
    def load_model(self, model_name: str, **kwargs) -> None:
        """加载模型"""
        pass

    @abstractmethod
    def encode_text(self, texts: Union[str, List[str]]) -> np.ndarray:
        """文本编码"""
        pass

    @abstractmethod
    def encode_image(self, images: Union[str, List[str]]) -> np.ndarray:
        """图像编码"""
        pass

    @abstractmethod
    def get_model_info(self) -> dict:
        """获取模型信息"""
        pass

    @abstractmethod
    def cleanup(self) -> None:
        """清理资源"""
        pass
```

#### Transformers后端实现
```python
# app/core/inference/transformers_backend.py
import torch
from transformers import CLIPModel, CLIPProcessor
from PIL import Image
import numpy as np
from .base import InferenceBackend

class TransformersBackend(InferenceBackend):
    """Transformers推理后端"""

    def __init__(self):
        self.model = None
        self.processor = None
        self.device = "cpu"  # CPU环境

    def load_model(self, model_name: str, **kwargs) -> None:
        """加载Transformers模型"""
        try:
            self.model = CLIPModel.from_pretrained(model_name)
            self.processor = CLIPProcessor.from_pretrained(model_name)

            # CPU优化设置
            self.model.eval()
            torch.set_num_threads(4)  # 设置CPU线程数

            print(f"Transformers模型加载成功: {model_name}")
        except Exception as e:
            raise RuntimeError(f"模型加载失败: {e}")

    def encode_text(self, texts: Union[str, List[str]]) -> np.ndarray:
        """文本编码"""
        if isinstance(texts, str):
            texts = [texts]

        with torch.no_grad():
            inputs = self.processor(text=texts, return_tensors="pt", padding=True)
            text_features = self.model.get_text_features(**inputs)
            # L2归一化
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)

        return text_features.cpu().numpy()

    def encode_image(self, images: Union[str, List[str]]) -> np.ndarray:
        """图像编码"""
        if isinstance(images, str):
            images = [images]

        # 加载图片
        pil_images = []
        for img_path in images:
            if isinstance(img_path, str):
                pil_images.append(Image.open(img_path).convert('RGB'))
            else:
                pil_images.append(img_path)

        with torch.no_grad():
            inputs = self.processor(images=pil_images, return_tensors="pt")
            image_features = self.model.get_image_features(**inputs)
            # L2归一化
            image_features = image_features / image_features.norm(dim=-1, keepdim=True)

        return image_features.cpu().numpy()

    def get_model_info(self) -> dict:
        """获取模型信息"""
        return {
            "backend": "transformers",
            "device": self.device,
            "model_loaded": self.model is not None
        }

    def cleanup(self) -> None:
        """清理资源"""
        if self.model:
            del self.model
            del self.processor
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
```

#### ONNX后端实现（CPU优化）
```python
# app/core/inference/onnx_backend.py
import onnxruntime as ort
import numpy as np
from PIL import Image
from transformers import CLIPTokenizer
from .base import InferenceBackend

class ONNXBackend(InferenceBackend):
    """ONNX推理后端 - CPU优化版本"""

    def __init__(self):
        self.text_session = None
        self.image_session = None
        self.tokenizer = None

    def load_model(self, model_name: str, **kwargs) -> None:
        """加载ONNX模型"""
        try:
            # CPU优化的ONNX运行时配置
            providers = ['CPUExecutionProvider']
            sess_options = ort.SessionOptions()
            sess_options.intra_op_num_threads = 4  # CPU线程数
            sess_options.inter_op_num_threads = 2
            sess_options.execution_mode = ort.ExecutionMode.ORT_SEQUENTIAL
            sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL

            # 加载文本和图像编码器
            text_model_path = kwargs.get('text_model_path', f"{model_name}/text_encoder.onnx")
            image_model_path = kwargs.get('image_model_path', f"{model_name}/image_encoder.onnx")

            self.text_session = ort.InferenceSession(
                text_model_path,
                providers=providers,
                sess_options=sess_options
            )
            self.image_session = ort.InferenceSession(
                image_model_path,
                providers=providers,
                sess_options=sess_options
            )

            # 加载tokenizer
            self.tokenizer = CLIPTokenizer.from_pretrained(model_name)

            print(f"ONNX模型加载成功: {model_name}")
        except Exception as e:
            raise RuntimeError(f"ONNX模型加载失败: {e}")

    def encode_text(self, texts: Union[str, List[str]]) -> np.ndarray:
        """文本编码"""
        if isinstance(texts, str):
            texts = [texts]

        # 文本预处理
        inputs = self.tokenizer(
            texts,
            padding=True,
            truncation=True,
            return_tensors="np"
        )

        # ONNX推理
        outputs = self.text_session.run(
            None,
            {
                "input_ids": inputs["input_ids"].astype(np.int64),
                "attention_mask": inputs["attention_mask"].astype(np.int64)
            }
        )

        text_features = outputs[0]
        # L2归一化
        text_features = text_features / np.linalg.norm(text_features, axis=-1, keepdims=True)

        return text_features

    def encode_image(self, images: Union[str, List[str]]) -> np.ndarray:
        """图像编码"""
        if isinstance(images, str):
            images = [images]

        # 图像预处理
        processed_images = []
        for img_path in images:
            if isinstance(img_path, str):
                image = Image.open(img_path).convert('RGB')
            else:
                image = img_path

            # 简单的预处理（需要根据实际模型调整）
            image = image.resize((224, 224))
            image_array = np.array(image).astype(np.float32) / 255.0
            image_array = (image_array - 0.5) / 0.5  # 归一化到[-1, 1]
            processed_images.append(image_array)

        batch_images = np.stack(processed_images)
        batch_images = np.transpose(batch_images, (0, 3, 1, 2))  # NCHW格式

        # ONNX推理
        outputs = self.image_session.run(None, {"pixel_values": batch_images})

        image_features = outputs[0]
        # L2归一化
        image_features = image_features / np.linalg.norm(image_features, axis=-1, keepdims=True)

        return image_features

    def get_model_info(self) -> dict:
        """获取模型信息"""
        return {
            "backend": "onnx",
            "device": "cpu",
            "providers": ["CPUExecutionProvider"],
            "model_loaded": self.text_session is not None and self.image_session is not None
        }

    def cleanup(self) -> None:
        """清理资源"""
        if self.text_session:
            del self.text_session
        if self.image_session:
            del self.image_session
```

#### 推理服务工厂
```python
# app/core/inference/factory.py
from typing import Dict, Type
from .base import InferenceBackend
from .transformers_backend import TransformersBackend
from .onnx_backend import ONNXBackend

class InferenceBackendFactory:
    """推理后端工厂类"""

    _backends: Dict[str, Type[InferenceBackend]] = {
        "transformers": TransformersBackend,
        "onnx": ONNXBackend,
    }

    @classmethod
    def create_backend(cls, backend_type: str) -> InferenceBackend:
        """创建推理后端实例"""
        if backend_type not in cls._backends:
            raise ValueError(f"不支持的推理后端: {backend_type}")

        backend_class = cls._backends[backend_type]
        return backend_class()

    @classmethod
    def register_backend(cls, name: str, backend_class: Type[InferenceBackend]) -> None:
        """注册新的推理后端"""
        cls._backends[name] = backend_class

    @classmethod
    def get_available_backends(cls) -> list:
        """获取可用的推理后端列表"""
        return list(cls._backends.keys())
```

### 单例模式 - 模型管理器

```python
# app/core/models.py
import threading
from typing import Optional
from .inference.factory import InferenceBackendFactory
from .inference.base import InferenceBackend

class ModelManager:
    """模型管理器 - 单例模式"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.backend: Optional[InferenceBackend] = None
            self.current_model = None
            self.current_backend_type = None
            self.initialized = True

    def load_model(self, model_name: str, backend_type: str = "transformers", **kwargs) -> None:
        """加载模型"""
        try:
            # 清理旧模型
            if self.backend:
                self.backend.cleanup()

            # 创建新的推理后端
            self.backend = InferenceBackendFactory.create_backend(backend_type)
            self.backend.load_model(model_name, **kwargs)

            self.current_model = model_name
            self.current_backend_type = backend_type

            print(f"模型切换成功: {model_name} ({backend_type})")

        except Exception as e:
            print(f"模型加载失败: {e}")
            raise

    def encode_text(self, texts):
        """文本编码"""
        if not self.backend:
            raise RuntimeError("模型未加载")
        return self.backend.encode_text(texts)

    def encode_image(self, images):
        """图像编码"""
        if not self.backend:
            raise RuntimeError("模型未加载")
        return self.backend.encode_image(images)

    def get_model_info(self) -> dict:
        """获取模型信息"""
        if not self.backend:
            return {"model_loaded": False}

        info = self.backend.get_model_info()
        info.update({
            "current_model": self.current_model,
            "backend_type": self.current_backend_type
        })
        return info

    def is_loaded(self) -> bool:
        """检查模型是否已加载"""
        return self.backend is not None
```

### 策略模式 - CPU优化策略

```python
# app/core/optimization/cpu_strategy.py
from abc import ABC, abstractmethod
import multiprocessing
import os

class OptimizationStrategy(ABC):
    """优化策略抽象基类"""

    @abstractmethod
    def apply_optimization(self) -> dict:
        """应用优化策略"""
        pass

class CPUOptimizationStrategy(OptimizationStrategy):
    """CPU优化策略"""

    def apply_optimization(self) -> dict:
        """应用CPU优化"""
        cpu_count = multiprocessing.cpu_count()

        # 设置CPU线程数
        if cpu_count >= 8:
            torch_threads = 4
            onnx_threads = 4
        elif cpu_count >= 4:
            torch_threads = 2
            onnx_threads = 2
        else:
            torch_threads = 1
            onnx_threads = 1

        # 设置环境变量
        os.environ['OMP_NUM_THREADS'] = str(torch_threads)
        os.environ['MKL_NUM_THREADS'] = str(torch_threads)

        return {
            "cpu_count": cpu_count,
            "torch_threads": torch_threads,
            "onnx_threads": onnx_threads,
            "optimization_applied": True
        }

class OptimizationContext:
    """优化上下文"""

    def __init__(self, strategy: OptimizationStrategy):
        self.strategy = strategy

    def set_strategy(self, strategy: OptimizationStrategy):
        """设置优化策略"""
        self.strategy = strategy

    def optimize(self) -> dict:
        """执行优化"""
        return self.strategy.apply_optimization()
```

### 模型加载流程
1. **配置解析**：解析模型配置参数
2. **后端选择**：根据配置选择推理后端
3. **模型下载**：自动下载或加载本地模型
4. **模型优化**：根据后端进行模型优化
5. **预热推理**：执行预热推理确保模型可用
6. **状态更新**：更新模型状态和性能指标

### 模型切换流程
1. **验证请求**：验证新模型配置的有效性
2. **预加载**：在后台预加载新模型
3. **性能测试**：对新模型进行性能测试
4. **原子切换**：原子性地切换到新模型
5. **清理旧模型**：释放旧模型占用的资源
6. **状态通知**：通知相关组件模型已切换

## 性能优化

### 模型加载优化
- **并行加载**：支持多个模型并行加载
- **增量加载**：只加载变更的模型组件
- **缓存机制**：缓存已加载的模型避免重复加载
- **内存映射**：使用内存映射减少内存占用

### 推理优化
- **批处理**：自动批处理多个推理请求
- **异步推理**：支持异步推理提高并发性能
- **设备调度**：智能调度推理任务到最优设备
- **内存池**：使用内存池减少内存分配开销

### 硬件适配
- **自动检测**：自动检测可用的硬件加速器
- **设备优先级**：cuda > xpu > mps > directml > cpu
- **性能基准**：提供benchmark工具进行性能测试
- **配置推荐**：根据硬件自动推荐最优配置

## 监控和维护

### 性能监控
- **推理延迟**：记录每次推理的耗时
- **吞吐量**：统计每秒处理的请求数
- **资源使用**：监控CPU、内存、GPU使用率
- **错误率**：统计推理失败的比例

### 模型健康检查
- **可用性检查**：定期检查模型是否可用
- **性能检查**：监控推理性能是否正常
- **内存检查**：检查模型内存使用是否异常
- **自动恢复**：检测到问题时自动重新加载模型

### 维护操作
- **模型更新**：支持模型版本更新
- **缓存清理**：定期清理模型缓存
- **性能调优**：根据监控数据调整配置
- **故障恢复**：模型故障时的自动恢复机制

## 扩展功能

### 模型版本管理
- **版本控制**：支持模型版本管理
- **回滚机制**：支持回滚到之前的模型版本
- **A/B测试**：支持多版本模型对比测试
- **渐进式部署**：支持新模型的渐进式部署

### 自定义模型支持
- **模型注册**：支持注册自定义训练的模型
- **格式转换**：支持多种模型格式的转换
- **兼容性检查**：检查自定义模型的兼容性
- **性能评估**：对自定义模型进行性能评估
